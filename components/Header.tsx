import { Image, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import React from "react";
import { Bell, Search } from "lucide-react-native";

const Header = () => {
  return (
    <View style={styles.header}>
      <View
        style={{ flexDirection: "row", alignItems: "center", marginTop: 26 }}>
        <Image
          source={require("@/assets/icons/dark.png")}
          style={{ width: 50, height: 50, marginRight: 16 }}
        />
        <View style={styles.searchContainer}>
          <Search color="#666" size={20} />
          <Text style={styles.searchPlaceholder}>Search</Text>
        </View>
        <TouchableOpacity style={styles.notificationButton}>
          <Bell color="#fff" size={24} />
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default Header;

const styles = StyleSheet.create({
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8,
    justifyContent: "space-between",
    backgroundColor: "#000",
  },
  searchContainer: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#1a1a1a",
    borderRadius: 8,
    padding: 12,
    marginRight: 16,
  },
  searchPlaceholder: {
    color: "#666",
    marginLeft: 8,
  },
  notificationButton: {
    padding: 8,
  },
});

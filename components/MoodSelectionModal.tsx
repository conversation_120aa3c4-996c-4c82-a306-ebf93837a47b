import React from "react";
import {
  Modal,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { X } from "lucide-react-native";

interface MoodCategory {
  title: string;
  emoji: string;
  moods: {
    name: string;
    description: string;
  }[];
  color: string;
}

const moodCategories: MoodCategory[] = [
  {
    title: "Positive Moods",
    emoji: "😊",
    color: "#FBF3DB", // Pastel Yellow
    moods: [
      { name: "Happy", description: "Feeling joy, satisfaction, or pleasure." },
      {
        name: "Excited",
        description: "High energy, anticipation, or enthusiasm.",
      },
      { name: "Content", description: "Peaceful, satisfied, and at ease." },
      { name: "Grateful", description: "Appreciation and thankfulness." },
      {
        name: "Loving",
        description: "Affectionate, compassionate, or caring.",
      },
      {
        name: "Optimistic",
        description: "Hopeful and positive about the future.",
      },
      { name: "Proud", description: "A sense of achievement or self-worth." },
    ],
  },
  {
    title: "Negative Moods",
    emoji: "😞",
    color: "#FDEBEC", // Pastel Red
    moods: [
      { name: "Sad", description: "Feeling down, low, or disappointed." },
      { name: "Angry", description: "Irritated, frustrated, or resentful." },
      { name: "Anxious", description: "Nervousness, worry, or fear." },
      { name: "Lonely", description: "Feeling isolated or disconnected." },
      {
        name: "Guilty",
        description: "Regretful or remorseful about something.",
      },
      {
        name: "Jealous",
        description: "Envious of someone's success or possessions.",
      },
      {
        name: "Frustrated",
        description: "Feeling stuck or annoyed due to obstacles.",
      },
    ],
  },
  {
    title: "Neutral Moods",
    emoji: "😐",
    color: "#E7F3F8", // Pastel Blue
    moods: [
      { name: "Calm", description: "Relaxed and composed." },
      { name: "Tired", description: "Fatigued or lacking energy." },
      {
        name: "Indifferent",
        description: "Uninterested or emotionally detached.",
      },
      { name: "Bored", description: "Lacking stimulation or excitement." },
      {
        name: "Curious",
        description: "Open to learning or discovering new things.",
      },
    ],
  },
  {
    title: "Complex Moods",
    emoji: "🤯",
    color: "#F1F1EF", // Pastel Gray
    moods: [
      {
        name: "Nostalgic",
        description: "A mix of happiness and longing for the past.",
      },
      { name: "Bittersweet", description: "A blend of joy and sadness." },
      { name: "Conflicted", description: "Torn between two or more emotions." },
      { name: "Melancholic", description: "A deep, reflective sadness." },
      { name: "Euphoric", description: "Extreme happiness or bliss." },
      {
        name: "Mood Swings",
        description: "Rapid shifts between different emotions.",
      },
    ],
  },
];

interface MoodSelectionModalProps {
  visible: boolean;
  onClose: () => void;
  onSelectMood: (mood: string) => void;
}

const MoodSelectionModal: React.FC<MoodSelectionModalProps> = ({
  visible,
  onClose,
  onSelectMood,
}) => {
  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}>
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Select Your Mood</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <X size={24} color="#666" />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.moodList}>
            {moodCategories.map((category, index) => (
              <View key={index} style={styles.categoryContainer}>
                <View
                  style={[
                    styles.categoryHeader,
                    { backgroundColor: category.color },
                  ]}>
                  <Text style={styles.categoryTitle}>
                    {category.emoji} {category.title}
                  </Text>
                </View>
                {category.moods.map((mood, moodIndex) => (
                  <TouchableOpacity
                    key={moodIndex}
                    style={[
                      styles.moodItem,
                      { backgroundColor: category.color },
                    ]}
                    onPress={() => {
                      onSelectMood(mood.name);
                      onClose();
                    }}>
                    <Text style={styles.moodName}>{mood.name}</Text>
                    <Text style={styles.moodDescription}>
                      {mood.description}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            ))}
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  modalContent: {
    backgroundColor: "#fff",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    height: "80%",
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: "bold",
  },
  closeButton: {
    padding: 8,
  },
  moodList: {
    flex: 1,
    padding: 16,
  },
  categoryContainer: {
    marginBottom: 24,
    borderRadius: 12,
    overflow: "hidden",
  },
  categoryHeader: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0, 0, 0, 0.1)",
  },
  categoryTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  moodItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0, 0, 0, 0.1)",
  },
  moodName: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 4,
  },
  moodDescription: {
    fontSize: 14,
    color: "#666",
  },
});

export default MoodSelectionModal;

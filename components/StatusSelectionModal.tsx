import React from "react";
import {
  Modal,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { X, Check, Circle } from "lucide-react-native";

interface StatusOption {
  value: "not-started" | "started" | "on-track" | "on-hold" | "completed";
  label: string;
  color: string;
}

const statusOptions: StatusOption[] = [
  { value: "not-started", label: "NOT STARTED", color: "#F44336" },
  { value: "started", label: "STARTED", color: "#FF9800" },
  { value: "on-track", label: "ON TRACK", color: "#2196F3" },
  { value: "on-hold", label: "ON HOLD", color: "#FFC107" },
  { value: "completed", label: "COMPLETED", color: "#4CAF50" },
];

interface StatusSelectionModalProps {
  visible: boolean;
  onClose: () => void;
  onSelectStatus: (status: StatusOption["value"]) => void;
  currentStatus: StatusOption["value"];
}

const StatusSelectionModal: React.FC<StatusSelectionModalProps> = ({
  visible,
  onClose,
  onSelectStatus,
  currentStatus,
}) => {
  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}>
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Select Status</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <X size={24} color="#666" />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.statusList}>
            {statusOptions.map((status) => (
              <TouchableOpacity
                key={status.value}
                style={[
                  styles.statusItem,
                  status.value === currentStatus && styles.selectedStatus,
                ]}
                onPress={() => {
                  onSelectStatus(status.value);
                  onClose();
                }}>
                <View style={styles.statusInfo}>
                  <View
                    style={[
                      styles.statusDot,
                      { backgroundColor: status.color },
                    ]}
                  />
                  <Text
                    style={[
                      styles.statusLabel,
                      status.value === currentStatus &&
                        styles.selectedStatusText,
                    ]}>
                    {status.label}
                  </Text>
                </View>
                {status.value === currentStatus ? (
                  <Check size={24} color="#4CAF50" />
                ) : (
                  <Circle size={24} color="#666" />
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  modalContent: {
    backgroundColor: "#fff",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    height: "50%",
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: "bold",
  },
  closeButton: {
    padding: 8,
  },
  statusList: {
    flex: 1,
    padding: 16,
  },
  statusItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  selectedStatus: {
    backgroundColor: "#E8F5E9",
  },
  statusInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  statusDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  statusLabel: {
    fontSize: 16,
    color: "#000",
  },
  selectedStatusText: {
    color: "#4CAF50",
    fontWeight: "500",
  },
});

export default StatusSelectionModal;

import React from "react";
import {
  Modal,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
} from "react-native";
import { X, Check, Circle } from "lucide-react-native";

interface Profile {
  id: string;
  name: string;
  role: string;
  department: string;
  image: any; // Using any for now, but in a real app, you'd want to type this properly
}

// Sample profiles - in a real app, these would come from your data source
const profiles: Profile[] = [
  {
    id: "1",
    name: "<PERSON>",
    role: "CF<PERSON>",
    department: "Administration",
    image: require("@/assets/icons/dark.png"),
  },
  {
    id: "2",
    name: "<PERSON>",
    role: "CEO",
    department: "Executive",
    image: require("@/assets/icons/dark.png"),
  },
  {
    id: "3",
    name: "<PERSON>",
    role: "CTO",
    department: "Technology",
    image: require("@/assets/icons/dark.png"),
  },
  {
    id: "4",
    name: "<PERSON>",
    role: "COO",
    department: "Operations",
    image: require("@/assets/icons/dark.png"),
  },
];

interface ProfileSelectionModalProps {
  visible: boolean;
  onClose: () => void;
  onSelectProfile: (profile: Profile) => void;
  currentProfileId: string;
}

const ProfileSelectionModal: React.FC<ProfileSelectionModalProps> = ({
  visible,
  onClose,
  onSelectProfile,
  currentProfileId,
}) => {
  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}>
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Switch Profile</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <X size={24} color="#666" />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.profileList}>
            {profiles.map((profile) => (
              <TouchableOpacity
                key={profile.id}
                style={[
                  styles.profileItem,
                  profile.id === currentProfileId && styles.selectedProfile,
                ]}
                onPress={() => {
                  onSelectProfile(profile);
                  onClose();
                }}>
                <View style={styles.profileInfo}>
                  <Image source={profile.image} style={styles.profileImage} />
                  <View style={styles.profileDetails}>
                    <Text style={styles.profileName}>{profile.name}</Text>
                    <Text style={styles.profileRole}>{profile.role}</Text>
                    <Text style={styles.profileDepartment}>
                      {profile.department}
                    </Text>
                  </View>
                </View>
                {profile.id === currentProfileId ? (
                  <Check size={24} color="#4CAF50" />
                ) : (
                  <Circle size={24} color="#666" />
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  modalContent: {
    backgroundColor: "#fff",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    height: "50%",
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: "bold",
  },
  closeButton: {
    padding: 8,
  },
  profileList: {
    flex: 1,
    padding: 16,
  },
  profileItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  selectedProfile: {
    backgroundColor: "#E8F5E9",
  },
  profileInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  profileImage: {
    width: 50,
    height: 50,
    borderRadius: 8,
    marginRight: 16,
  },
  profileDetails: {
    flex: 1,
  },
  profileName: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 4,
  },
  profileRole: {
    fontSize: 14,
    color: "#666",
    marginBottom: 2,
  },
  profileDepartment: {
    fontSize: 14,
    color: "#666",
  },
});

export default ProfileSelectionModal;

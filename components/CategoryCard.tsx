import { StyleSheet, Text, View, TouchableOpacity } from "react-native";
import React from "react";
import {
  <PERSON><PERSON>he<PERSON>,
  ClipboardList,
  Clock,
  Newspaper,
  Plane,
  FolderOpen,
  Files,
  Coins,
  Bitcoin,
  TrendingUp,
  Rocket,
  FileText,
  Lightbulb,
  Activity,
  Users,
  Radio,
  Folder,
  Wallet,
  Receipt,
  FileSpreadsheet,
  DollarSign,
  CreditCard,
  Building2,
  CalendarDays,
  Briefcase,
  UserPlus,
  Users2,
  LogOut,
  ShoppingBag,
  Megaphone,
  FileVideo,
  Handshake,
  UserCircle2,
  Target,
  MonitorSmartphone,
  Puzzle,
  Bug,
  Ticket,
  HelpCircle,
  LucideIcon,
} from "lucide-react-native";

export const categoryColors = {
  HOME: "#787774",
  OPERATIONS: "#337EA9",
  ADMINISTRATION: "#9065B0",
  FINANCE: "#448361",
  PEOPLE: "#C14C8A",
  MSSS: "#CB912F",
} as const;

interface CategoryCardProps {
  title: keyof typeof categoryColors;
  items: string[];
  onItemPress?: (item: string) => void;
}

type IconMapType = {
  [key: string]: {
    [key: string]: LucideIcon;
  };
};

const getItemIcon = (category: string, item: string): LucideIcon => {
  const iconMap: IconMapType = {
    HOME: {
      Tasks: ClipboardList,
      Attendance: Clock,
      News: Newspaper,
      Trips: Plane,
      Resources: FolderOpen,
      Files: Files,
    },
    OPERATIONS: {
      Coins: Coins,
      Crypto: Bitcoin,
      Stocks: TrendingUp,
      Startups: Rocket,
    },
    ADMINISTRATION: {
      Plan: FileText,
      Strategy: Lightbulb,
      Activity: Activity,
      Partners: Users,
      Channels: Radio,
      Resources: Folder,
    },
    FINANCE: {
      Budget: Wallet,
      Transactions: Receipt,
      Invoices: FileSpreadsheet,
      Expenses: DollarSign,
      Salaries: CreditCard,
      Payees: Building2,
      Bank: Building2,
    },
    PEOPLE: {
      Leave: CalendarDays,
      Job: Briefcase,
      Openings: UserPlus,
      Employees: Users2,
      Onboarding: UserPlus,
      Offboarding: LogOut,
    },
    MSSS: {
      Market: ShoppingBag,
      Campaigns: Megaphone,
      Content: FileVideo,
      Deals: Handshake,
      Clients: UserCircle2,
      Competitors: Target,
      Platform: MonitorSmartphone,
      Features: Puzzle,
      Bugs: Bug,
      Tickets: Ticket,
      Support: HelpCircle,
    },
  };

  const DefaultIcon = CircleCheck;
  return iconMap[category]?.[item] || DefaultIcon;
};

const getItemStatus = (category: string, item: string): { text: string } => {
  const defaultStatus = { text: "Active" };

  const statusMap: { [key: string]: { [key: string]: { text: string } } } = {
    HOME: {
      Tasks: { text: "Pending" },
      Attendance: { text: "Present" },
    },
    OPERATIONS: {
      Stocks: { text: "Trading" },
      Crypto: { text: "Volatile" },
    },
    FINANCE: {
      Budget: { text: "Review" },
      Transactions: { text: "Pending" },
    },
    PEOPLE: {
      Leave: { text: "Open" },
      Job: { text: "Hiring" },
    },
    MSSS: {
      Bugs: { text: "Critical" },
      Tickets: { text: "Open" },
    },
  };

  return statusMap[category]?.[item] || defaultStatus;
};

const CategoryCard: React.FC<CategoryCardProps> = ({
  title,
  items,
  onItemPress,
}) => (
  <View style={styles.container}>
    <Text style={[styles.title, { color: categoryColors[title] }]}>
      {title}
    </Text>
    <View style={styles.itemsContainer}>
      {items.map((item, index) => {
        const Icon = getItemIcon(title, item);
        const status = getItemStatus(title, item);
        return (
          <TouchableOpacity
            key={index}
            style={styles.item}
            onPress={() => onItemPress && onItemPress(item)}>
            <View style={styles.itemContent}>
              <Icon size={24} color="#000" />
              <View style={styles.itemTextContainer}>
                <Text style={styles.itemText}>{item}</Text>
                <Text style={styles.itemStatus}>{status.text}</Text>
              </View>
            </View>
          </TouchableOpacity>
        );
      })}
    </View>
  </View>
);

const styles = StyleSheet.create({
  container: {
    marginTop: 8,
  },
  title: {
    padding: 16,
    backgroundColor: "#fff",
    marginBottom: 2,
    fontSize: 15,
    fontWeight: "bold",
  },
  itemsContainer: {
    padding: 16,
    backgroundColor: "#fff",
  },
  item: {
    padding: 16,
    backgroundColor: "#fff",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 2,
  },
  itemContent: {
    flexDirection: "row",
    alignItems: "center",
    gap: 16,
  },
  itemTextContainer: {
    gap: 4,
  },
  itemText: {
    fontSize: 17,
    fontWeight: "bold",
  },
  itemStatus: {
    fontSize: 15,
    color: "#666",
  },
});

export default CategoryCard;

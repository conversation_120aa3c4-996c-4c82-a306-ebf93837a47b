import React, { useState, useEffect } from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Modal,
  ScrollView,
  Dimensions,
} from "react-native";
import { useRouter } from "expo-router";
import { useAuth } from "../context/auth";
import { mcpService } from "@/lib/appwrite/mcp-integration";

const { width, height } = Dimensions.get("window");

interface OnboardingStep {
  id: string;
  name: string;
  completed: boolean;
}

interface OnboardingProps {
  visible: boolean;
  onClose: () => void;
}

export default function Onboarding({ visible, onClose }: OnboardingProps) {
  const router = useRouter();
  const { user } = useAuth();
  const [steps, setSteps] = useState<OnboardingStep[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible && user) {
      loadOnboardingData();
    }
  }, [visible, user]);

  const loadOnboardingData = async () => {
    if (!user) return;

    try {
      const checklist = await mcpService.getUserProfile(user.$id);
      // In a real implementation, you'd fetch the onboarding checklist
      // For now, we'll use default steps
      setSteps([
        { id: "profile", name: "Complete Profile", completed: false },
        { id: "preferences", name: "Set Preferences", completed: false },
        { id: "first_tracker", name: "Create First Tracker Entry", completed: false },
        { id: "explore_features", name: "Explore Features", completed: false },
      ]);
    } catch (error) {
      console.error("Error loading onboarding data:", error);
    }
  };

  const handleStepPress = async (stepId: string) => {
    if (!user) return;

    setLoading(true);
    try {
      switch (stepId) {
        case "profile":
          router.push("/(platform)/profile");
          break;
        case "preferences":
          router.push("/(platform)/profile");
          break;
        case "first_tracker":
          router.push("/(platform)/home/<USER>");
          break;
        case "explore_features":
          router.push("/(platform)/home");
          break;
      }

      // Mark step as completed
      await mcpService.updateOnboardingStep(user.$id, stepId, true);
      
      // Update local state
      setSteps(prev => 
        prev.map(step => 
          step.id === stepId ? { ...step, completed: true } : step
        )
      );

      onClose();
    } catch (error) {
      console.error("Error handling step:", error);
    } finally {
      setLoading(false);
    }
  };

  const completedSteps = steps.filter(step => step.completed).length;
  const totalSteps = steps.length;
  const progress = totalSteps > 0 ? (completedSteps / totalSteps) * 100 : 0;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Welcome to Sharif!</Text>
          <Text style={styles.subtitle}>
            Let's get you started with a quick setup
          </Text>
          
          <View style={styles.progressContainer}>
            <View style={styles.progressBar}>
              <View style={[styles.progressFill, { width: `${progress}%` }]} />
            </View>
            <Text style={styles.progressText}>
              {completedSteps} of {totalSteps} completed
            </Text>
          </View>
        </View>

        <ScrollView style={styles.stepsContainer}>
          {steps.map((step, index) => (
            <TouchableOpacity
              key={step.id}
              style={[
                styles.stepItem,
                step.completed && styles.stepCompleted
              ]}
              onPress={() => handleStepPress(step.id)}
              disabled={loading}
            >
              <View style={styles.stepNumber}>
                <Text style={[
                  styles.stepNumberText,
                  step.completed && styles.stepNumberTextCompleted
                ]}>
                  {step.completed ? "✓" : index + 1}
                </Text>
              </View>
              
              <View style={styles.stepContent}>
                <Text style={[
                  styles.stepName,
                  step.completed && styles.stepNameCompleted
                ]}>
                  {step.name}
                </Text>
                <Text style={styles.stepDescription}>
                  {getStepDescription(step.id)}
                </Text>
              </View>

              <View style={styles.stepArrow}>
                <Text style={styles.stepArrowText}>→</Text>
              </View>
            </TouchableOpacity>
          ))}
        </ScrollView>

        <View style={styles.footer}>
          <TouchableOpacity
            style={styles.skipButton}
            onPress={onClose}
          >
            <Text style={styles.skipButtonText}>Skip for now</Text>
          </TouchableOpacity>
          
          {progress === 100 && (
            <TouchableOpacity
              style={styles.completeButton}
              onPress={onClose}
            >
              <Text style={styles.completeButtonText}>Get Started!</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </Modal>
  );
}

function getStepDescription(stepId: string): string {
  switch (stepId) {
    case "profile":
      return "Set up your profile and preferences";
    case "preferences":
      return "Customize your app experience";
    case "first_tracker":
      return "Create your first daily tracker entry";
    case "explore_features":
      return "Discover what Sharif can do for you";
    default:
      return "";
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000",
  },
  header: {
    padding: 20,
    paddingTop: 60,
    borderBottomWidth: 1,
    borderBottomColor: "#333",
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#fff",
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: "#888",
    marginBottom: 20,
  },
  progressContainer: {
    marginBottom: 10,
  },
  progressBar: {
    height: 6,
    backgroundColor: "#333",
    borderRadius: 3,
    marginBottom: 8,
  },
  progressFill: {
    height: "100%",
    backgroundColor: "#fff",
    borderRadius: 3,
  },
  progressText: {
    fontSize: 14,
    color: "#888",
    textAlign: "center",
  },
  stepsContainer: {
    flex: 1,
    padding: 20,
  },
  stepItem: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#1e1e1e",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: "#333",
  },
  stepCompleted: {
    backgroundColor: "#0a3d0a",
    borderColor: "#4CAF50",
  },
  stepNumber: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "#333",
    alignItems: "center",
    justifyContent: "center",
    marginRight: 16,
  },
  stepNumberText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "bold",
  },
  stepNumberTextCompleted: {
    color: "#4CAF50",
  },
  stepContent: {
    flex: 1,
  },
  stepName: {
    fontSize: 16,
    fontWeight: "600",
    color: "#fff",
    marginBottom: 4,
  },
  stepNameCompleted: {
    color: "#4CAF50",
  },
  stepDescription: {
    fontSize: 14,
    color: "#888",
  },
  stepArrow: {
    marginLeft: 12,
  },
  stepArrowText: {
    fontSize: 18,
    color: "#666",
  },
  footer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: "#333",
  },
  skipButton: {
    height: 50,
    backgroundColor: "transparent",
    borderWidth: 1,
    borderColor: "#666",
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 12,
  },
  skipButtonText: {
    color: "#666",
    fontSize: 16,
  },
  completeButton: {
    height: 50,
    backgroundColor: "#fff",
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
  },
  completeButtonText: {
    color: "#000",
    fontSize: 16,
    fontWeight: "bold",
  },
});

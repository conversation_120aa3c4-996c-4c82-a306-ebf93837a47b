import React from "react";
import {
  Modal,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { X, Check, Circle } from "lucide-react-native";

interface Prayer {
  name: string;
  arabic: string;
  time: string;
  rakahs: string;
  significance: string;
  emoji: string;
}

const prayers: Prayer[] = [
  {
    name: "<PERSON>aj<PERSON>",
    arabic: "الفجر",
    time: "Before sunrise",
    rakahs: "2 Sunnah + 2 Fard",
    significance: "A fresh start to the day with remembrance of <PERSON>",
    emoji: "🌅",
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    arabic: "الظهر",
    time: "After midday until afternoon",
    rakahs: "4 Sunnah + 4 Fard + 2 Sunnah + 2 Nafl",
    significance: "A moment of pause and connection with <PERSON> during the day",
    emoji: "🌞",
  },
  {
    name: "Asr",
    arabic: "العصر",
    time: "Late afternoon until sunset",
    rakahs: "4 Sunnah + 4 Fard",
    significance: "A reminder to stay mindful of <PERSON> in the busy hours",
    emoji: "☀️",
  },
  {
    name: "<PERSON><PERSON><PERSON><PERSON>",
    arabic: "المغرب",
    time: "After sunset until dusk",
    rakahs: "3 Fard + 2 Sunnah + 2 Nafl",
    significance: "A time of gratitude as the day ends",
    emoji: "🌇",
  },
  {
    name: "Isha",
    arabic: "العشاء",
    time: "After dusk until midnight",
    rakahs: "4 Sunnah + 4 Fard + 2 Sunnah + 2 Nafl + Witr",
    significance: "The final prayer of the day, bringing peace and reflection",
    emoji: "🌙",
  },
  {
    name: "Witr",
    arabic: "وتر",
    time: "After Isha",
    rakahs: "3 (or odd numbers)",
    significance: "Highly recommended prayer after Isha",
    emoji: "✨",
  },
  {
    name: "Tahajjud",
    arabic: "تَهَجُّد",
    time: "Late night voluntary prayer",
    rakahs: "2 or more (even numbers)",
    significance: "A voluntary prayer with great rewards",
    emoji: "🌠",
  },
];

interface ValueSelectionModalProps {
  visible: boolean;
  onClose: () => void;
  onSelectValue: (value: string) => void;
  type:
    | "prayers"
    | "steps_count"
    | "water"
    | "work_hours"
    | "phone_hours"
    | "sleep_hours";
  currentValue: string;
}

const ValueSelectionModal: React.FC<ValueSelectionModalProps> = ({
  visible,
  onClose,
  onSelectValue,
  type,
  currentValue,
}) => {
  const getOptions = () => {
    switch (type) {
      case "prayers":
        return prayers;
      case "steps_count":
        return Array.from({ length: 21 }, (_, i) => `${i * 1000} STEPS`);
      case "water":
        return Array.from({ length: 5 }, (_, i) => `${i + 1} LITERS`);
      case "work_hours":
        return Array.from({ length: 13 }, (_, i) => `${i + 4} HRS`);
      case "phone_hours":
        return Array.from({ length: 13 }, (_, i) => `${i + 1} HRS`);
      case "sleep_hours":
        return Array.from({ length: 13 }, (_, i) => `${i + 4} HRS`);
      default:
        return [];
    }
  };

  const getTitle = () => {
    switch (type) {
      case "prayers":
        return "Select Prayers";
      case "steps_count":
        return "Select Steps";
      case "water":
        return "Select Water Intake";
      case "work_hours":
        return "Select Work Hours";
      case "phone_hours":
        return "Select Phone Hours";
      case "sleep_hours":
        return "Select Sleep Hours";
      default:
        return "Select Value";
    }
  };

  const renderPrayerItem = (prayer: Prayer) => {
    const isCompleted = currentValue.includes(prayer.name);
    return (
      <TouchableOpacity
        key={prayer.name}
        style={[styles.prayerItem, isCompleted && styles.completedPrayer]}
        onPress={() => {
          const newValue = isCompleted
            ? currentValue.replace(prayer.name, "").trim()
            : `${currentValue} ${prayer.name}`.trim();
          onSelectValue(newValue);
        }}>
        <View style={styles.prayerHeader}>
          <View style={styles.prayerTitleContainer}>
            <Text style={styles.prayerName}>
              {prayer.emoji} {prayer.name}
            </Text>
            <Text style={styles.prayerArabic}>{prayer.arabic}</Text>
          </View>
          {isCompleted ? (
            <Check size={24} color="#4CAF50" />
          ) : (
            <Circle size={24} color="#666" />
          )}
        </View>
        <Text style={styles.prayerTime}>{prayer.time}</Text>
        <Text style={styles.prayerRakahs}>{prayer.rakahs}</Text>
        <Text style={styles.prayerSignificance}>{prayer.significance}</Text>
      </TouchableOpacity>
    );
  };

  const renderContent = () => {
    if (type === "prayers") {
      return (
        <ScrollView style={styles.valueList}>
          {prayers.map((prayer) => renderPrayerItem(prayer))}
        </ScrollView>
      );
    }

    const options = getOptions() as string[];
    return (
      <ScrollView style={styles.valueList}>
        {options.map((value, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.valueItem,
              value === currentValue && styles.selectedValue,
            ]}
            onPress={() => {
              onSelectValue(value);
              onClose();
            }}>
            <Text
              style={[
                styles.valueText,
                value === currentValue && styles.selectedValueText,
              ]}>
              {value}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}>
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>{getTitle()}</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <X size={24} color="#666" />
            </TouchableOpacity>
          </View>

          {renderContent()}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  modalContent: {
    backgroundColor: "#fff",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    height: "90%",
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: "bold",
  },
  closeButton: {
    padding: 8,
  },
  valueList: {
    flex: 1,
    padding: 16,
  },
  valueItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  selectedValue: {
    backgroundColor: "#4CAF50",
  },
  valueText: {
    fontSize: 16,
    color: "#000",
  },
  selectedValueText: {
    color: "#fff",
  },
  prayerItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
    backgroundColor: "#fff",
  },
  completedPrayer: {
    backgroundColor: "#E8F5E9",
  },
  prayerHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  prayerTitleContainer: {
    flex: 1,
  },
  prayerName: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 4,
  },
  prayerArabic: {
    fontSize: 16,
    color: "#666",
    marginBottom: 4,
  },
  prayerTime: {
    fontSize: 14,
    color: "#666",
    marginBottom: 4,
  },
  prayerRakahs: {
    fontSize: 14,
    color: "#666",
    marginBottom: 4,
  },
  prayerSignificance: {
    fontSize: 14,
    color: "#666",
    fontStyle: "italic",
  },
});

export default ValueSelectionModal;

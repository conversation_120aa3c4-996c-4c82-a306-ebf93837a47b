import React, { useEffect, useRef, useState } from "react";
import {
  Modal,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Image,
  ScrollView,
  Animated,
  Dimensions,
} from "react-native";
import { X, Image as ImageIcon } from "lucide-react-native";
import * as ImagePicker from "expo-image-picker";

interface AddBudgetModalProps {
  visible: boolean;
  onClose: () => void;
  onSave: (budget: {
    name: string;
    goal: string;
    walletId: string;
    imageUri?: string;
  }) => void;
  wallets: Array<{ id: string; name: string; balance: string }>;
}

const AddBudgetModal: React.FC<AddBudgetModalProps> = ({
  visible,
  onClose,
  onSave,
  wallets,
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(300)).current;
  const [budgetName, setBudgetName] = useState("");
  const [budgetGoal, setBudgetGoal] = useState("");
  const [selectedWallet, setSelectedWallet] = useState("");
  const [budgetImage, setBudgetImage] = useState<string | null>(null);

  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 300,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible]);

  const pickImage = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 1,
    });

    if (!result.canceled) {
      setBudgetImage(result.assets[0].uri);
    }
  };

  const handleSave = () => {
    if (!budgetName || !budgetGoal || !selectedWallet) {
      // Show error message
      return;
    }

    onSave({
      name: budgetName,
      goal: budgetGoal,
      walletId: selectedWallet,
      imageUri: budgetImage || undefined,
    });

    // Reset form
    setBudgetName("");
    setBudgetGoal("");
    setSelectedWallet("");
    setBudgetImage(null);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={onClose}>
      <View style={styles.modalContainer}>
        <Animated.View
          style={[
            styles.modalContent,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}>
          <View style={styles.header}>
            <Text style={styles.title}>Add New Budget</Text>
            <TouchableOpacity onPress={onClose}>
              <X size={24} color="#000" />
            </TouchableOpacity>
          </View>

          <ScrollView
            style={styles.form}
            contentContainerStyle={styles.formContent}>
            <TouchableOpacity style={styles.imagePicker} onPress={pickImage}>
              {budgetImage ? (
                <Image
                  source={{ uri: budgetImage }}
                  style={styles.budgetImage}
                />
              ) : (
                <View style={styles.imagePlaceholder}>
                  <ImageIcon size={24} color="#666" />
                  <Text style={styles.imagePlaceholderText}>
                    Add Budget Image
                  </Text>
                </View>
              )}
            </TouchableOpacity>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Budget Name</Text>
              <TextInput
                style={styles.input}
                value={budgetName}
                onChangeText={setBudgetName}
                placeholder="Enter budget name"
                placeholderTextColor="#999"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Budget Goal</Text>
              <TextInput
                style={styles.input}
                value={budgetGoal}
                onChangeText={setBudgetGoal}
                placeholder="Enter budget goal"
                placeholderTextColor="#999"
                keyboardType="numeric"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Select Wallet</Text>
              <View style={styles.walletList}>
                {wallets.map((wallet) => (
                  <TouchableOpacity
                    key={wallet.id}
                    style={[
                      styles.walletItem,
                      selectedWallet === wallet.id && styles.selectedWallet,
                    ]}
                    onPress={() => setSelectedWallet(wallet.id)}>
                    <Text style={styles.walletName}>{wallet.name}</Text>
                    <Text style={styles.walletBalance}>{wallet.balance}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </ScrollView>

          <View style={styles.footer}>
            <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
              <Text style={styles.saveButtonText}>Save</Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  modalContent: {
    backgroundColor: "#fff",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    maxHeight: Dimensions.get("window").height * 0.8,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
  },
  form: {
    flex: 1,
  },
  formContent: {
    paddingBottom: 20,
  },
  imagePicker: {
    alignItems: "center",
    marginBottom: 20,
  },
  budgetImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  imagePlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: "#f5f5f5",
    justifyContent: "center",
    alignItems: "center",
  },
  imagePlaceholderText: {
    marginTop: 8,
    color: "#666",
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    color: "#666",
  },
  input: {
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: "#fff",
  },
  walletList: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  walletItem: {
    padding: 12,
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    minWidth: "48%",
    backgroundColor: "#fff",
  },
  selectedWallet: {
    borderColor: "#D9730D",
    backgroundColor: "#FFF5EB",
  },
  walletName: {
    fontSize: 14,
    fontWeight: "bold",
  },
  walletBalance: {
    fontSize: 12,
    color: "#666",
    marginTop: 4,
  },
  footer: {
    flexDirection: "row",
    justifyContent: "flex-end",
    gap: 12,
    marginTop: 20,
    borderTopWidth: 1,
    borderTopColor: "#eee",
    paddingTop: 20,
  },
  cancelButton: {
    padding: 12,
  },
  cancelButtonText: {
    color: "#666",
    fontSize: 16,
  },
  saveButton: {
    backgroundColor: "#D9730D",
    padding: 12,
    borderRadius: 8,
  },
  saveButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "bold",
  },
});

export default AddBudgetModal;

import { StyleSheet, Text, View, Image } from "react-native";
import React from "react";

interface FamilyMemberProps {
  name: string;
  relation: string;
}

const FamilyMember = ({ name, relation }: FamilyMemberProps) => {
  return (
    <View style={styles.container}>
      <View style={styles.imageContainer}>
        <View style={styles.imagePlaceholder} />
      </View>
      <Text style={styles.name}>{name}</Text>
      <Text style={styles.relation}>{relation}</Text>
    </View>
  );
};

export default FamilyMember;

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    backgroundColor: "white",
    borderRadius: 10,
    padding: 10,
    width: "100%",
    aspectRatio: 0.8,
    borderWidth: 2,
    borderColor: "#E0E0E0",
  },
  imageContainer: {
    width: "80%",
    aspectRatio: 1,
    marginBottom: 18,
    marginTop: 15,
  },
  imagePlaceholder: {
    width: "100%",
    height: "100%",
    borderRadius: 999,
    backgroundColor: "#f0f0f0",
  },
  name: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 2,
  },
  relation: {
    fontSize: 12,
    color: "#666",
    textTransform: "uppercase",
  },
});

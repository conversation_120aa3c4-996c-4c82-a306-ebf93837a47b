import { StyleSheet, Text, View, ScrollView } from "react-native";
import React from "react";
import TaskCard from "./TaskCard";

interface Task {
  title: string;
  status: "not-started" | "started" | "on-track" | "on-hold" | "completed";
}

interface CategorySectionProps {
  title: string;
  timeRange: string;
  tasks: Task[];
  backgroundColor?: string;
  onStatusChange?: (taskIndex: number, newStatus: Task["status"]) => void;
}

const CategorySection = ({
  title,
  timeRange,
  tasks,
  backgroundColor = "#F8F9FA",
  onStatusChange,
}: CategorySectionProps) => {
  return (
    <View style={[styles.container, { backgroundColor }]}>
      <View style={styles.header}>
        <Text style={styles.title}>{title}</Text>
        <Text style={styles.timeRange}>{timeRange}</Text>
      </View>
      <ScrollView
        style={styles.tasksScrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.tasksContainer}>
        {tasks.map((task, index) => (
          <TaskCard
            key={index}
            title={task.title}
            status={task.status}
            onStatusChange={(newStatus) => onStatusChange?.(index, newStatus)}
          />
        ))}
      </ScrollView>
    </View>
  );
};

export default CategorySection;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    borderRadius: 16,
    margin: 8,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: "700",
    color: "#1A1A1A",
  },
  timeRange: {
    fontSize: 14,
    color: "#666666",
    fontWeight: "500",
  },
  tasksScrollView: {
    flex: 1,
  },
  tasksContainer: {
    gap: 4,
    paddingBottom: 16,
  },
});

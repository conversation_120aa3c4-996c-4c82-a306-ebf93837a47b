import React, { useState } from "react";
import {
  Modal,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import { X, Save } from "lucide-react-native";

interface JournalModalProps {
  visible: boolean;
  onClose: () => void;
  onSave: (journalText: string) => void;
  initialText?: string;
}

const JournalModal: React.FC<JournalModalProps> = ({
  visible,
  onClose,
  onSave,
  initialText = "",
}) => {
  const [journalText, setJournalText] = useState(initialText);
  const maxLength = 255;
  const remainingChars = maxLength - journalText.length;

  const handleSave = () => {
    onSave(journalText);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Journal Entry</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <X size={24} color="#666" />
            </TouchableOpacity>
          </View>

          <View style={styles.inputContainer}>
            <TextInput
              style={styles.textInput}
              multiline
              placeholder="Write about your day and feelings..."
              value={journalText}
              onChangeText={setJournalText}
              maxLength={maxLength}
              autoFocus
            />
            <Text style={styles.charCount}>
              {remainingChars} characters remaining
            </Text>
          </View>

          <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
            <Save size={20} color="#fff" style={styles.saveIcon} />
            <Text style={styles.saveButtonText}>Save</Text>
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  modalContent: {
    backgroundColor: "#fff",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    height: "70%",
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: "bold",
  },
  closeButton: {
    padding: 8,
  },
  inputContainer: {
    flex: 1,
    padding: 16,
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: "#F1F1EF",
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    textAlignVertical: "top",
    minHeight: 200,
  },
  charCount: {
    textAlign: "right",
    marginTop: 8,
    color: "#666",
    fontSize: 12,
  },
  saveButton: {
    flexDirection: "row",
    backgroundColor: "#4CAF50",
    borderRadius: 8,
    padding: 16,
    margin: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  saveButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "bold",
  },
  saveIcon: {
    marginRight: 8,
  },
});

export default JournalModal;

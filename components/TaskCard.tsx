import { StyleSheet, Text, View, TouchableOpacity } from "react-native";
import React, { useState } from "react";
import { Circle } from "lucide-react-native";
import StatusSelectionModal from "@/components/StatusSelectionModal";

interface TaskCardProps {
  title: string;
  status: "not-started" | "started" | "on-track" | "on-hold" | "completed";
  onStatusChange?: (newStatus: TaskCardProps["status"]) => void;
}

const TaskCard = ({ title, status, onStatusChange }: TaskCardProps) => {
  const [showStatusModal, setShowStatusModal] = useState(false);

  const getStatusColor = () => {
    switch (status) {
      case "completed":
        return "#4CAF50";
      case "on-track":
        return "#2196F3";
      case "on-hold":
        return "#FFC107";
      case "started":
        return "#FF9800";
      case "not-started":
        return "#F44336";
      default:
        return "#757575";
    }
  };

  const getStatusText = () => {
    switch (status) {
      case "completed":
        return "COMPLETED";
      case "on-track":
        return "ON TRACK";
      case "on-hold":
        return "ON HOLD";
      case "started":
        return "STARTED";
      case "not-started":
        return "NOT STARTED";
      default:
        return "";
    }
  };

  const handleStatusChange = (newStatus: TaskCardProps["status"]) => {
    onStatusChange?.(newStatus);
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <View style={styles.iconPlaceholder} />
          <Text style={styles.title}>{title}</Text>
        </View>
        <Circle color={getStatusColor()} size={24} />
      </View>
      <TouchableOpacity
        style={[
          styles.statusBar,
          { backgroundColor: status === "completed" ? "#E8F5E9" : "#F5F5F5" },
        ]}
        onPress={() => setShowStatusModal(true)}>
        <Text style={[styles.statusText, { color: getStatusColor() }]}>
          {getStatusText()}
        </Text>
      </TouchableOpacity>

      <StatusSelectionModal
        visible={showStatusModal}
        onClose={() => setShowStatusModal(false)}
        onSelectStatus={handleStatusChange}
        currentStatus={status}
      />
    </View>
  );
};

export default TaskCard;

const styles = StyleSheet.create({
  container: {
    backgroundColor: "white",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  titleContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  iconPlaceholder: {
    width: 35,
    height: 35,
    backgroundColor: "#F5F5F5",
    borderRadius: 8,
    marginRight: 12,
  },
  title: {
    fontSize: 16,
    fontWeight: "600",
  },
  statusBar: {
    padding: 8,
    borderRadius: 8,
    alignItems: "center",
  },
  statusText: {
    fontSize: 14,
    fontWeight: "500",
  },
});

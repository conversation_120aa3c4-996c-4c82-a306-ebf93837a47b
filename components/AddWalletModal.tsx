import React, { useState, useRef } from "react";
import {
  Modal,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Image,
  Animated,
  ScrollView,
} from "react-native";
import { X, Upload } from "lucide-react-native";

interface AddWalletModalProps {
  visible: boolean;
  onClose: () => void;
  onSave: (wallet: { name: string; balance: string; imageUri: string }) => void;
}

const AddWalletModal: React.FC<AddWalletModalProps> = ({
  visible,
  onClose,
  onSave,
}) => {
  const [walletName, setWalletName] = useState("");
  const [balance, setBalance] = useState("");
  const [imageUri, setImageUri] = useState<string | null>(null);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(300)).current;

  React.useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 300,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible]);

  // For now, we'll use a placeholder image instead of actual image picking
  const pickImage = () => {
    // In a real app, you would implement image picking here
    // For now, we'll just use a placeholder
    setImageUri("https://via.placeholder.com/100");
  };

  const handleSave = () => {
    if (walletName.trim() === "" || balance.trim() === "") {
      // Show validation error
      return;
    }

    onSave({
      name: walletName,
      balance: balance,
      imageUri: imageUri || "",
    });

    // Reset form
    setWalletName("");
    setBalance("");
    setImageUri(null);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="none"
      onRequestClose={onClose}>
      <TouchableOpacity
        activeOpacity={1}
        style={styles.modalContainer}
        onPress={onClose}>
        <Animated.View
          style={[
            styles.modalOverlay,
            {
              opacity: fadeAnim,
            },
          ]}>
          <TouchableOpacity
            activeOpacity={1}
            onPress={(e) => e.stopPropagation()}>
            <Animated.View
              style={[
                styles.modalContent,
                {
                  transform: [{ translateY: slideAnim }],
                },
              ]}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Add New Wallet</Text>
                <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                  <X size={24} color="#666" />
                </TouchableOpacity>
              </View>

              <ScrollView style={styles.formContainer}>
                {/* Wallet Image */}
                <View style={styles.imageContainer}>
                  {imageUri ? (
                    <Image
                      source={{ uri: imageUri }}
                      style={styles.walletImage}
                    />
                  ) : (
                    <View style={styles.imagePlaceholder}>
                      <Upload size={24} color="#666" />
                    </View>
                  )}
                  <TouchableOpacity
                    style={styles.uploadButton}
                    onPress={pickImage}>
                    <Text style={styles.uploadButtonText}>
                      {imageUri ? "Change Image" : "Upload Image"}
                    </Text>
                  </TouchableOpacity>
                </View>

                {/* Wallet Name */}
                <View style={styles.inputContainer}>
                  <Text style={styles.inputLabel}>Wallet Name</Text>
                  <TextInput
                    style={styles.input}
                    placeholder="Enter wallet name"
                    value={walletName}
                    onChangeText={setWalletName}
                  />
                </View>

                {/* Current Balance */}
                <View style={styles.inputContainer}>
                  <Text style={styles.inputLabel}>Current Balance</Text>
                  <TextInput
                    style={styles.input}
                    placeholder="Enter current balance"
                    value={balance}
                    onChangeText={setBalance}
                    keyboardType="numeric"
                  />
                </View>

                {/* Save Button */}
                <TouchableOpacity
                  style={styles.saveButton}
                  onPress={handleSave}>
                  <Text style={styles.saveButtonText}>Save Wallet</Text>
                </TouchableOpacity>
              </ScrollView>
            </Animated.View>
          </TouchableOpacity>
        </Animated.View>
      </TouchableOpacity>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: "transparent",
    justifyContent: "flex-end",
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  modalContent: {
    backgroundColor: "#fff",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: "80%",
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: "bold",
  },
  closeButton: {
    padding: 8,
  },
  formContainer: {
    padding: 16,
  },
  imageContainer: {
    alignItems: "center",
    marginBottom: 24,
  },
  walletImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    marginBottom: 12,
  },
  imagePlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: "#f5f5f5",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 12,
  },
  uploadButton: {
    padding: 8,
    backgroundColor: "#f5f5f5",
    borderRadius: 8,
  },
  uploadButtonText: {
    color: "#D9730D",
    fontWeight: "500",
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  saveButton: {
    backgroundColor: "#D9730D",
    borderRadius: 8,
    padding: 16,
    alignItems: "center",
    marginTop: 24,
    marginBottom: 24,
  },
  saveButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "bold",
  },
});

export default AddWalletModal;

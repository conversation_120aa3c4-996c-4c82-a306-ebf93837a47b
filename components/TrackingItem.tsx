import React from "react";
import { View, Text, StyleSheet } from "react-native";
import { LucideIcon } from "lucide-react-native";

interface TrackingItemProps {
  icon: LucideIcon;
  label: string;
  status?: string;
  value?: string;
  statusIcon?: LucideIcon;
}

const TrackingItem: React.FC<TrackingItemProps> = ({
  icon: Icon,
  label,
  status,
  value,
  statusIcon: StatusIcon,
}) => {
  return (
    <View style={styles.container}>
      <View style={styles.leftContent}>
        <Icon size={24} color="#000" />
        <Text style={styles.label}>{label}</Text>
      </View>
      <View style={styles.rightContent}>
        {status ? (
          <View style={styles.statusContainer}>
            <Text
              style={[styles.status, status === "DONE" && styles.doneStatus]}>
              {status}
            </Text>
            {StatusIcon && (
              <StatusIcon size={24} color="#4CAF50" style={styles.statusIcon} />
            )}
          </View>
        ) : (
          <Text style={styles.value}>{value}</Text>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    marginHorizontal: 12,
    backgroundColor: "#fff",
    borderRadius: 8,
  },
  leftContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  label: {
    marginLeft: 26,
    fontSize: 17,
    fontWeight: "600",
  },
  rightContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  statusContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  statusIcon: {
    marginLeft: 6,
  },
  status: {
    fontSize: 16,
    fontWeight: "600",
  },
  doneStatus: {
    color: "#4CAF50",
  },
  value: {
    fontSize: 16,
    fontWeight: "600",
  },
});

export default TrackingItem;

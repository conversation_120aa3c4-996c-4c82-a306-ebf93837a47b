import { Client, Databases, ID, Query } from "react-native-appwrite";
import { client } from "@/config/appwrite";

const databases = new Databases(client);

const DATABASE_ID = "sharif_db";
const COLLECTION_ID = "tracker_data";

export interface TrackerData {
  date: string;
  userId: string;
  // Checklist items
  wakeup: boolean;
  exercise: boolean;
  diet: boolean;
  learn: boolean;
  work: boolean;
  finances: boolean;
  steps: boolean;
  sleep: boolean;
  // Mood
  selectedMood: string;
  // Values
  prayers: string;
  steps_count: string;
  water: string;
  work_hours: string;
  phone_hours: string;
  sleep_hours: string;
  // Journal
  journal?: string;
  // Income
  income: string;
}

export const createTrackerData = async (data: TrackerData) => {
  try {
    console.log("Creating tracker data with:", JSON.stringify(data, null, 2));
    const response = await databases.createDocument(
      DATABASE_ID,
      COLLECTION_ID,
      ID.unique(),
      data,
    );
    console.log("Create tracker data response:", response);
    return response;
  } catch (error) {
    console.error("Error creating tracker data:", error);
    if (error instanceof Error) {
      console.error("Error details:", error.message);
      console.error("Error stack:", error.stack);
    }
    throw error;
  }
};

export const getTrackerData = async (date: string, userId: string) => {
  try {
    const response = await databases.listDocuments(DATABASE_ID, COLLECTION_ID, [
      Query.equal("date", date),
      Query.equal("userId", userId),
    ]);
    return response.documents[0]
      ? (response.documents[0] as unknown as TrackerData)
      : null;
  } catch (error) {
    console.error("Error getting tracker data:", error);
    throw error;
  }
};

export const updateTrackerData = async (
  documentId: string,
  data: TrackerData,
) => {
  try {
    console.log("Updating tracker data for document:", documentId);
    console.log("Update data:", JSON.stringify(data, null, 2));
    const response = await databases.updateDocument(
      DATABASE_ID,
      COLLECTION_ID,
      documentId,
      data,
    );
    console.log("Update tracker data response:", response);
    return response;
  } catch (error) {
    console.error("Error updating tracker data:", error);
    if (error instanceof Error) {
      console.error("Error details:", error.message);
      console.error("Error stack:", error.stack);
    }
    throw error;
  }
};

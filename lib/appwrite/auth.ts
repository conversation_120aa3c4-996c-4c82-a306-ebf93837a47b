import { Client, Account, ID, Models } from "react-native-appwrite";
import { client, account } from "@/config/appwrite";

export interface SignUpData {
  email: string;
  password: string;
  name: string;
}

export interface SignInData {
  email: string;
  password: string;
}

export class AuthService {
  private account: Account;

  constructor() {
    this.account = account;
  }

  /**
   * Create a new user account
   */
  async signUp({ email, password, name }: SignUpData): Promise<Models.User<Models.Preferences>> {
    try {
      // Create the account
      await this.account.create(ID.unique(), email, password, name);
      
      // Automatically sign in the user
      await this.account.createEmailPasswordSession(email, password);
      
      // Get and return user data
      const user = await this.account.get();
      return user;
    } catch (error: any) {
      console.error("Sign up error:", error);
      throw new Error(this.getErrorMessage(error));
    }
  }

  /**
   * Sign in an existing user
   */
  async signIn({ email, password }: SignInData): Promise<Models.User<Models.Preferences>> {
    try {
      await this.account.createEmailPasswordSession(email, password);
      const user = await this.account.get();
      return user;
    } catch (error: any) {
      console.error("Sign in error:", error);
      throw new Error(this.getErrorMessage(error));
    }
  }

  /**
   * Sign out the current user
   */
  async signOut(): Promise<void> {
    try {
      await this.account.deleteSession("current");
    } catch (error: any) {
      console.error("Sign out error:", error);
      throw new Error(this.getErrorMessage(error));
    }
  }

  /**
   * Get current user session
   */
  async getCurrentUser(): Promise<Models.User<Models.Preferences> | null> {
    try {
      const user = await this.account.get();
      return user;
    } catch (error) {
      return null;
    }
  }

  /**
   * Send password recovery email
   */
  async sendPasswordRecovery(email: string): Promise<void> {
    try {
      await this.account.createRecovery(
        email,
        "http://localhost:3000/reset-password" // You'll need to update this URL
      );
    } catch (error: any) {
      console.error("Password recovery error:", error);
      throw new Error(this.getErrorMessage(error));
    }
  }

  /**
   * Update user password
   */
  async updatePassword(newPassword: string, oldPassword: string): Promise<void> {
    try {
      await this.account.updatePassword(newPassword, oldPassword);
    } catch (error: any) {
      console.error("Update password error:", error);
      throw new Error(this.getErrorMessage(error));
    }
  }

  /**
   * Update user name
   */
  async updateName(name: string): Promise<Models.User<Models.Preferences>> {
    try {
      const user = await this.account.updateName(name);
      return user;
    } catch (error: any) {
      console.error("Update name error:", error);
      throw new Error(this.getErrorMessage(error));
    }
  }

  /**
   * Update user email
   */
  async updateEmail(email: string, password: string): Promise<Models.User<Models.Preferences>> {
    try {
      const user = await this.account.updateEmail(email, password);
      return user;
    } catch (error: any) {
      console.error("Update email error:", error);
      throw new Error(this.getErrorMessage(error));
    }
  }

  /**
   * Get user preferences
   */
  async getPreferences(): Promise<Models.Preferences> {
    try {
      const prefs = await this.account.getPrefs();
      return prefs;
    } catch (error: any) {
      console.error("Get preferences error:", error);
      throw new Error(this.getErrorMessage(error));
    }
  }

  /**
   * Update user preferences
   */
  async updatePreferences(prefs: object): Promise<Models.User<Models.Preferences>> {
    try {
      const user = await this.account.updatePrefs(prefs);
      return user;
    } catch (error: any) {
      console.error("Update preferences error:", error);
      throw new Error(this.getErrorMessage(error));
    }
  }

  /**
   * Get all active sessions
   */
  async getSessions(): Promise<Models.SessionList> {
    try {
      const sessions = await this.account.listSessions();
      return sessions;
    } catch (error: any) {
      console.error("Get sessions error:", error);
      throw new Error(this.getErrorMessage(error));
    }
  }

  /**
   * Delete a specific session
   */
  async deleteSession(sessionId: string): Promise<void> {
    try {
      await this.account.deleteSession(sessionId);
    } catch (error: any) {
      console.error("Delete session error:", error);
      throw new Error(this.getErrorMessage(error));
    }
  }

  /**
   * Delete all sessions except current
   */
  async deleteAllSessions(): Promise<void> {
    try {
      await this.account.deleteSessions();
    } catch (error: any) {
      console.error("Delete all sessions error:", error);
      throw new Error(this.getErrorMessage(error));
    }
  }

  /**
   * Convert Appwrite error to user-friendly message
   */
  private getErrorMessage(error: any): string {
    if (error.code) {
      switch (error.code) {
        case 401:
          return "Invalid credentials. Please check your email and password.";
        case 409:
          return "An account with this email already exists.";
        case 429:
          return "Too many requests. Please try again later.";
        case 400:
          if (error.message.includes("password")) {
            return "Password must be at least 8 characters long.";
          }
          if (error.message.includes("email")) {
            return "Please enter a valid email address.";
          }
          return "Invalid input. Please check your information.";
        default:
          return error.message || "An unexpected error occurred.";
      }
    }
    return error.message || "An unexpected error occurred.";
  }
}

// Export a singleton instance
export const authService = new AuthService();

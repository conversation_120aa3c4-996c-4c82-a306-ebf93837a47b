import { account } from "@/config/appwrite";

/**
 * Test Appwrite connectivity
 */
export async function testAppwriteConnection(): Promise<boolean> {
  try {
    console.log("Testing Appwrite connection to: http://31.97.229.201/v1");
    console.log("Project ID: 6892321a001a1e4a17d4");

    // Try to get current session (this will fail if not logged in, but should connect)
    await account.get();
    console.log("✅ Appwrite connection successful - user is logged in");
    return true;
  } catch (error: any) {
    console.log("Testing connection with different method...");
    console.log("Error details:", error);

    // If getting current user fails, try creating a session with invalid credentials
    // This should give us a proper error response if the server is reachable
    try {
      await account.createEmailPasswordSession("<EMAIL>", "invalid");
    } catch (authError: any) {
      console.log("Auth test error:", authError);

      if (authError.code === 401 || authError.message.includes("Invalid credentials")) {
        console.log("✅ Appwrite server is reachable - got expected auth error");
        return true;
      } else if (authError.message.includes("Network request failed")) {
        console.error("❌ Network error - Appwrite server not reachable:", authError);
        return false;
      } else if (authError.message.includes("fetch")) {
        console.error("❌ Fetch error - possible CORS or network issue:", authError);
        return false;
      } else {
        console.log("✅ Appwrite server is reachable - got error:", authError.message);
        return true;
      }
    }

    return false;
  }
}

/**
 * Test function to be called from the app
 */
export async function runConnectionTest(): Promise<void> {
  const isConnected = await testAppwriteConnection();
  
  if (isConnected) {
    console.log("✅ Appwrite server is accessible");
  } else {
    console.error("❌ Cannot reach Appwrite server");
    console.error("Please check:");
    console.error("1. Server URL: http://31.97.229.201/v1");
    console.error("2. Network connectivity");
    console.error("3. Server is running");
  }
}

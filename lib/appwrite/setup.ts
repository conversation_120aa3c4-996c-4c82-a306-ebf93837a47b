import { Client, Databases, Permission, Role } from "react-native-appwrite";
import { client } from "@/config/appwrite";

/**
 * Database Setup Service
 * This service helps set up the required collections for the MCP integration
 */
export class DatabaseSetupService {
  private databases: Databases;
  private databaseId = "sharif_db";

  constructor() {
    this.databases = new Databases(client);
  }

  /**
   * Setup all required collections
   */
  async setupCollections(): Promise<void> {
    try {
      await Promise.all([
        this.createUserProfilesCollection(),
        this.createOnboardingChecklistsCollection(),
        this.createUserActivitiesCollection(),
        this.createNotificationsCollection(),
        this.createUserPreferencesCollection(),
      ]);
      console.log("All collections setup completed");
    } catch (error) {
      console.error("Error setting up collections:", error);
      throw error;
    }
  }

  /**
   * Create user profiles collection
   */
  private async createUserProfilesCollection(): Promise<void> {
    try {
      await this.databases.createCollection(
        this.databaseId,
        "user_profiles",
        "User Profiles",
        [
          Permission.read(Role.user("USER_ID")),
          Permission.write(Role.user("USER_ID")),
          Permission.delete(Role.user("USER_ID")),
        ]
      );

      // Add attributes
      await Promise.all([
        this.databases.createStringAttribute(this.databaseId, "user_profiles", "userId", 255, true),
        this.databases.createStringAttribute(this.databaseId, "user_profiles", "name", 255, true),
        this.databases.createStringAttribute(this.databaseId, "user_profiles", "email", 255, true),
        this.databases.createStringAttribute(this.databaseId, "user_profiles", "createdAt", 255, true),
        this.databases.createStringAttribute(this.databaseId, "user_profiles", "updatedAt", 255, false),
        this.databases.createBooleanAttribute(this.databaseId, "user_profiles", "isActive", true),
        this.databases.createBooleanAttribute(this.databaseId, "user_profiles", "profileComplete", false),
      ]);

      console.log("User profiles collection created");
    } catch (error: any) {
      if (error.code !== 409) { // Collection already exists
        throw error;
      }
    }
  }

  /**
   * Create onboarding checklists collection
   */
  private async createOnboardingChecklistsCollection(): Promise<void> {
    try {
      await this.databases.createCollection(
        this.databaseId,
        "onboarding_checklists",
        "Onboarding Checklists",
        [
          Permission.read(Role.user("USER_ID")),
          Permission.write(Role.user("USER_ID")),
          Permission.delete(Role.user("USER_ID")),
        ]
      );

      // Add attributes
      await Promise.all([
        this.databases.createStringAttribute(this.databaseId, "onboarding_checklists", "userId", 255, true),
        this.databases.createStringAttribute(this.databaseId, "onboarding_checklists", "createdAt", 255, true),
        this.databases.createStringAttribute(this.databaseId, "onboarding_checklists", "updatedAt", 255, false),
        this.databases.createStringAttribute(this.databaseId, "onboarding_checklists", "completedAt", 255, false),
      ]);

      console.log("Onboarding checklists collection created");
    } catch (error: any) {
      if (error.code !== 409) {
        throw error;
      }
    }
  }

  /**
   * Create user activities collection
   */
  private async createUserActivitiesCollection(): Promise<void> {
    try {
      await this.databases.createCollection(
        this.databaseId,
        "user_activities",
        "User Activities",
        [
          Permission.read(Role.user("USER_ID")),
          Permission.write(Role.user("USER_ID")),
        ]
      );

      // Add attributes
      await Promise.all([
        this.databases.createStringAttribute(this.databaseId, "user_activities", "userId", 255, true),
        this.databases.createStringAttribute(this.databaseId, "user_activities", "type", 255, true),
        this.databases.createStringAttribute(this.databaseId, "user_activities", "description", 1000, true),
        this.databases.createStringAttribute(this.databaseId, "user_activities", "timestamp", 255, true),
      ]);

      console.log("User activities collection created");
    } catch (error: any) {
      if (error.code !== 409) {
        throw error;
      }
    }
  }

  /**
   * Create notifications collection
   */
  private async createNotificationsCollection(): Promise<void> {
    try {
      await this.databases.createCollection(
        this.databaseId,
        "notifications",
        "Notifications",
        [
          Permission.read(Role.user("USER_ID")),
          Permission.write(Role.user("USER_ID")),
          Permission.delete(Role.user("USER_ID")),
        ]
      );

      // Add attributes
      await Promise.all([
        this.databases.createStringAttribute(this.databaseId, "notifications", "userId", 255, true),
        this.databases.createStringAttribute(this.databaseId, "notifications", "type", 255, true),
        this.databases.createStringAttribute(this.databaseId, "notifications", "title", 255, true),
        this.databases.createStringAttribute(this.databaseId, "notifications", "message", 1000, true),
        this.databases.createStringAttribute(this.databaseId, "notifications", "createdAt", 255, true),
        this.databases.createBooleanAttribute(this.databaseId, "notifications", "read", false),
      ]);

      console.log("Notifications collection created");
    } catch (error: any) {
      if (error.code !== 409) {
        throw error;
      }
    }
  }

  /**
   * Create user preferences collection
   */
  private async createUserPreferencesCollection(): Promise<void> {
    try {
      await this.databases.createCollection(
        this.databaseId,
        "user_preferences",
        "User Preferences",
        [
          Permission.read(Role.user("USER_ID")),
          Permission.write(Role.user("USER_ID")),
          Permission.delete(Role.user("USER_ID")),
        ]
      );

      // Add attributes
      await Promise.all([
        this.databases.createStringAttribute(this.databaseId, "user_preferences", "userId", 255, true),
        this.databases.createStringAttribute(this.databaseId, "user_preferences", "createdAt", 255, true),
        this.databases.createStringAttribute(this.databaseId, "user_preferences", "updatedAt", 255, false),
      ]);

      console.log("User preferences collection created");
    } catch (error: any) {
      if (error.code !== 409) {
        throw error;
      }
    }
  }

  /**
   * Check if database exists and create if needed
   */
  async ensureDatabaseExists(): Promise<void> {
    try {
      // Try to list collections to check if database exists
      await this.databases.listCollections(this.databaseId);
      console.log("Database exists");
    } catch (error: any) {
      if (error.code === 404) {
        try {
          await this.databases.create(this.databaseId, "Sharif Database");
          console.log("Database created");
        } catch (createError) {
          console.error("Error creating database:", createError);
          throw createError;
        }
      } else {
        throw error;
      }
    }
  }

  /**
   * Initialize the entire database setup
   */
  async initialize(): Promise<void> {
    try {
      await this.ensureDatabaseExists();
      await this.setupCollections();
      console.log("Database initialization completed");
    } catch (error) {
      console.error("Database initialization failed:", error);
      throw error;
    }
  }
}

// Export a singleton instance
export const databaseSetup = new DatabaseSetupService();

/**
 * Helper function to initialize database on app start
 */
export async function initializeDatabase(): Promise<void> {
  try {
    await databaseSetup.initialize();
  } catch (error) {
    console.warn("Database initialization failed, but app will continue:", error);
    // Don't throw error to prevent app crash
  }
}

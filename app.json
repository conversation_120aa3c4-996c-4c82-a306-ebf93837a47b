{"expo": {"name": "<PERSON>", "slug": "sharif", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icons/dark.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "icon": {"dark": "./assets/icons/dark.png", "light": "./assets/icons/light.png"}, "bundleIdentifier": "com.codelude.sharif"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/icons/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.codelude.sharif"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/icons/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/icons/splash-icon-dark.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff", "darkImage": {"image": "./assets/icons/splash-icon-light.png", "backgroundColor": "#000000"}}], "expo-font"], "experiments": {"typedRoutes": true}}}
import { Client, Account, ID, Databases } from "react-native-appwrite";

// Initialize Appwrite client
// Using Appwrite Cloud for testing - replace with your server when network issues are resolved
const client = new Client()
  .setProject("6892321a001a1e4a17d4") // Keep your project ID
  .setEndpoint("https://cloud.appwrite.io/v1") // Use Appwrite Cloud temporarily
  .setPlatform("com.codelude.sharif");

const account = new Account(client);
const databases = new Databases(client);

export { client, account, databases, ID };

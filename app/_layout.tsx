import { Slot, useRouter, useSegments } from "expo-router";
import { useEffect } from "react";
import { AuthProvider, useAuth } from "../context/auth";
import { runConnectionTest } from "../lib/appwrite/test";
// import { initializeDatabase } from "../lib/appwrite/setup";

function RootLayoutNav() {
  const { user, loading } = useAuth();
  const segments = useSegments();
  const router = useRouter();

  useEffect(() => {
    if (loading) return;

    const inAuthGroup = segments[0] === "(public)";
    const inPlatformGroup = segments[0] === "(platform)";

    if (!user && !inAuthGroup) {
      // Redirect to the public index page if not authenticated
      router.replace("/(public)");
    } else if (user && !inPlatformGroup) {
      // Redirect to the platform home page if authenticated
      router.replace("/(platform)/home");
    }
  }, [user, loading, segments]);

  if (loading) {
    return null; // Or a loading spinner
  }

  return <Slot />;
}

export default function RootLayout() {
  useEffect(() => {
    // Test Appwrite connection on app start
    runConnectionTest();

    // Initialize database on app start
    // Temporarily disabled for debugging
    // initializeDatabase();
  }, []);

  return (
    <AuthProvider>
      <RootLayoutNav />
    </AuthProvider>
  );
}

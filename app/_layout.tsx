import { Slot, Stack, useRouter, useSegments } from "expo-router";
import { useEffect } from "react";
import { AuthProvider, useAuth } from "../context/auth";

function RootLayoutNav() {
  const { user, loading } = useAuth();
  const segments = useSegments();
  const router = useRouter();

  useEffect(() => {
    if (loading) return;

    const inAuthGroup = segments[0] === "(public)";
    const inPlatformGroup = segments[0] === "(platform)";

    if (!user && !inAuthGroup) {
      // Redirect to the public index page if not authenticated
      router.replace("/(public)");
    } else if (user && !inPlatformGroup) {
      // Redirect to the platform home page if authenticated
      router.replace("/(platform)/home");
    }
  }, [user, loading, segments]);

  if (loading) {
    return null; // Or a loading spinner
  }

  return <Slot />;
}

export default function RootLayout() {
  return (
    <AuthProvider>
      <RootLayoutNav />
    </AuthProvider>
  );
}

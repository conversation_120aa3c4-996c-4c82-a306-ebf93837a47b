import { StatusB<PERSON> } from "expo-status-bar";
import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  Alert,
  Image,
} from "react-native";
import { Client, Account, ID, Models } from "react-native-appwrite";
import React, { useState } from "react";
import { useRouter } from "expo-router";
import { useAuth } from "../../context/auth";

import { client, account } from "@/config/appwrite";

export default function SignIn() {
  const router = useRouter();
  const [loggedInUser, setLoggedInUser] =
    useState<Models.User<Models.Preferences> | null>(null);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [name, setName] = useState("");
  const [isRegistering, setIsRegistering] = useState(false);
  const [error, setError] = useState("");
  const { signIn } = useAuth();

  async function login(email: string, password: string) {
    try {
      await account.createEmailPasswordSession(email, password);
      const user = await account.get();
      setLoggedInUser(user);
      router.replace("/(platform)/home");
    } catch (error) {
      Alert.alert("Error", "Invalid email or password");
    }
  }

  async function register(email: string, password: string, name: string) {
    try {
      await account.create(ID.unique(), email, password, name);
      await login(email, password);
    } catch (error) {
      Alert.alert("Error", "Registration failed. Please try again.");
    }
  }

  const handleSignIn = async () => {
    try {
      await signIn(email, password);
      setError("");
    } catch (err) {
      setError("Invalid email or password");
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar style="auto" />
      <Image
        source={require("@/assets/images/login.png")}
        style={styles.cover}
      />

      <View style={styles.form}>
        {isRegistering && (
          <TextInput
            style={styles.input}
            placeholder="Name"
            value={name}
            onChangeText={setName}
            autoCapitalize="words"
            autoFocus
            keyboardAppearance="dark"
          />
        )}

        {error && <Text style={styles.error}>{error}</Text>}

        <TextInput
          style={styles.input}
          placeholder="Email"
          value={email}
          onChangeText={setEmail}
          keyboardType="email-address"
          autoCapitalize="none"
          placeholderTextColor="#fff"
          autoFocus={!isRegistering}
          keyboardAppearance="dark"
        />

        <TextInput
          style={styles.input}
          placeholder="Password"
          placeholderTextColor="#fff"
          value={password}
          onChangeText={setPassword}
          secureTextEntry
          keyboardAppearance="dark"
        />

        <TouchableOpacity
          style={styles.button}
          onPress={() => {
            if (isRegistering) {
              register(email, password, name);
            } else {
              handleSignIn();
            }
          }}>
          <Text style={styles.buttonText}>
            {isRegistering ? "Register" : "Login"}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.switchButton}
          onPress={() => {
            if (isRegistering) {
              setIsRegistering(false);
            } else {
              router.push("/(public)/signup");
            }
          }}>
          <Text style={styles.switchButtonText}>
            {isRegistering
              ? "Already have an account? Login"
              : "Don't have an account? Register"}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000",
    alignItems: "center",
  },
  cover: {
    width: "100%",
    height: 300,
    marginBottom: 20,
  },
  form: {
    width: "100%",
    paddingHorizontal: 35,
  },
  input: {
    width: "100%",
    height: 50,
    borderWidth: 1,
    borderColor: "#ddd",
    color: "#fff",
    backgroundColor: "#1e1e1e",
    borderRadius: 8,
    paddingHorizontal: 15,
    marginBottom: 15,
    fontSize: 16,
  },
  button: {
    width: "100%",
    height: 50,
    backgroundColor: "#fff",
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
    marginTop: 15,
  },
  buttonText: {
    color: "#000",
    fontSize: 16,
    fontWeight: "bold",
    textTransform: "uppercase",
  },
  switchButton: {
    marginTop: 20,
  },
  switchButtonText: {
    color: "#007AFF",
    fontSize: 14,
  },
  error: {
    color: "red",
    marginBottom: 10,
    textAlign: "center",
  },
});

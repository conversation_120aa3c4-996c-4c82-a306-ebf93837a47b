import { StatusBar } from "expo-status-bar";
import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  Alert,
  Image,
} from "react-native";
import React, { useState } from "react";
import { useRouter } from "expo-router";
import { Client, Account, ID } from "react-native-appwrite";

// Simple Appwrite configuration - using demo project for testing
const client = new Client()
  .setProject("demo") // Demo project
  .setEndpoint("https://demo.appwrite.io/v1") // Demo endpoint
  .setPlatform("com.codelude.sharif");

const account = new Account(client);

export default function SimpleAuth() {
  const router = useRouter();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [name, setName] = useState("");
  const [isRegistering, setIsRegistering] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleLogin = async () => {
    if (!email || !password) {
      Alert.alert("Error", "Please fill in all fields");
      return;
    }

    setLoading(true);
    try {
      console.log("Attempting login...");
      await account.createEmailPasswordSession(email, password);
      const user = await account.get();
      console.log("Login successful:", user);
      Alert.alert("Success", "Logged in successfully!");
      router.replace("/(platform)/home");
    } catch (error: any) {
      console.error("Login error:", error);
      Alert.alert("Error", error.message || "Login failed");
    } finally {
      setLoading(false);
    }
  };

  const handleRegister = async () => {
    if (!email || !password || !name) {
      Alert.alert("Error", "Please fill in all fields");
      return;
    }

    if (password.length < 8) {
      Alert.alert("Error", "Password must be at least 8 characters");
      return;
    }

    setLoading(true);
    try {
      console.log("Attempting registration...");
      await account.create(ID.unique(), email, password, name);
      console.log("Registration successful, logging in...");
      await account.createEmailPasswordSession(email, password);
      const user = await account.get();
      console.log("Auto-login successful:", user);
      Alert.alert("Success", "Account created and logged in successfully!");
      router.replace("/(platform)/home");
    } catch (error: any) {
      console.error("Registration error:", error);
      Alert.alert("Error", error.message || "Registration failed");
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar style="light" />
      
      <Image
        source={require("@/assets/images/login.png")}
        style={styles.cover}
      />

      <View style={styles.form}>
        <Text style={styles.title}>
          {isRegistering ? "Create Account" : "Welcome Back"}
        </Text>

        {isRegistering && (
          <TextInput
            style={styles.input}
            placeholder="Full Name"
            placeholderTextColor="#888"
            value={name}
            onChangeText={setName}
            autoCapitalize="words"
          />
        )}

        <TextInput
          style={styles.input}
          placeholder="Email"
          placeholderTextColor="#888"
          value={email}
          onChangeText={setEmail}
          keyboardType="email-address"
          autoCapitalize="none"
        />

        <TextInput
          style={styles.input}
          placeholder="Password"
          placeholderTextColor="#888"
          value={password}
          onChangeText={setPassword}
          secureTextEntry
        />

        <TouchableOpacity
          style={[styles.button, loading && styles.buttonDisabled]}
          onPress={isRegistering ? handleRegister : handleLogin}
          disabled={loading}
        >
          <Text style={styles.buttonText}>
            {loading 
              ? (isRegistering ? "Creating Account..." : "Signing In...") 
              : (isRegistering ? "Create Account" : "Sign In")
            }
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.switchButton}
          onPress={() => setIsRegistering(!isRegistering)}
        >
          <Text style={styles.switchButtonText}>
            {isRegistering
              ? "Already have an account? Sign In"
              : "Don't have an account? Create Account"}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000",
    alignItems: "center",
  },
  cover: {
    width: "100%",
    height: 300,
    marginBottom: 20,
  },
  form: {
    width: "100%",
    paddingHorizontal: 35,
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#fff",
    textAlign: "center",
    marginBottom: 30,
  },
  input: {
    width: "100%",
    height: 50,
    borderWidth: 1,
    borderColor: "#333",
    color: "#fff",
    backgroundColor: "#1e1e1e",
    borderRadius: 8,
    paddingHorizontal: 15,
    marginBottom: 15,
    fontSize: 16,
  },
  button: {
    width: "100%",
    height: 50,
    backgroundColor: "#fff",
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
    marginTop: 15,
  },
  buttonDisabled: {
    backgroundColor: "#666",
  },
  buttonText: {
    color: "#000",
    fontSize: 16,
    fontWeight: "bold",
    textTransform: "uppercase",
  },
  switchButton: {
    marginTop: 20,
    alignItems: "center",
  },
  switchButtonText: {
    color: "#007AFF",
    fontSize: 14,
  },
});

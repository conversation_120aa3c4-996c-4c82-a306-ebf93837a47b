import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from "react-native";
import React, { useState, useEffect } from "react";
import DateTimePicker from "@react-native-community/datetimepicker";

import {
  Sun,
  Moon,
  Timer,
  Coffee,
  BookOpen,
  Laptop,
  DollarSign,
  Dumbbell,
  BedDouble,
  ChevronDown,
  ChevronUp,
  Smile,
  Utensils,
  Wallet,
  Footprints,
  Droplets,
  Clock,
  Smartphone,
  CircleCheck,
  Circle,
} from "lucide-react-native";
import Header from "@/components/Header";
import TrackingItem from "@/components/TrackingItem";
import MoodSelectionModal from "@/components/MoodSelectionModal";
import ValueSelectionModal from "@/components/ValueSelectionModal";
import JournalModal from "@/components/JournalModal";
import {
  createTrackerData,
  getTrackerData,
  updateTrackerData,
  TrackerData,
} from "@/lib/appwrite/database";
import { useAuth } from "@/context/auth";

const scheduleItems = [
  { id: 1, title: "WAKEUP", time: "05:30 AM", icon: Sun },
  { id: 2, title: "FAJR NAMAZ", time: "05:45 AM", icon: Moon },
  { id: 3, title: "RUN", time: "06:00 AM", icon: Timer },
  { id: 4, title: "BREAKFAST", time: "07:00 AM", icon: Coffee },
  { id: 5, title: "LEARN", time: "08:00 AM", icon: BookOpen },
  { id: 6, title: "WORK", time: "08:00 AM", icon: Laptop },
  // Add other schedule items as needed
];

const HomeLayout = () => {
  const { user } = useAuth();
  const [isScheduleExpanded, setIsScheduleExpanded] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showMoodModal, setShowMoodModal] = useState(false);
  const [showJournalModal, setShowJournalModal] = useState(false);
  const [journalText, setJournalText] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [selectedMood, setSelectedMood] = useState("Happy");
  const [showValueModal, setShowValueModal] = useState(false);
  const [valueModalType, setValueModalType] = useState<
    | "prayers"
    | "steps_count"
    | "water"
    | "work_hours"
    | "phone_hours"
    | "sleep_hours"
  >("prayers");
  const [selectedValues, setSelectedValues] = useState({
    prayers: "",
    steps_count: "0",
    water: "3 LITERS",
    work_hours: "8.9 HRS",
    phone_hours: "3 HRS",
    sleep_hours: "8 HRS",
  });
  const [checklistItems, setChecklistItems] = useState({
    wakeup: false,
    exercise: false,
    diet: false,
    learn: false,
    work: false,
    finances: false,
    steps: false,
    sleep: false,
  });
  const [currentDocumentId, setCurrentDocumentId] = useState<string | null>(
    null,
  );

  useEffect(() => {
    loadTrackerData();
  }, [selectedDate]);

  const loadTrackerData = async () => {
    try {
      if (!user?.$id) return;

      const dateStr = selectedDate.toISOString().split("T")[0];
      const data = await getTrackerData(dateStr, user.$id);

      if (data) {
        setSelectedMood(data.selectedMood);
        setSelectedValues({
          prayers: data.prayers,
          steps_count: data.steps_count,
          water: data.water,
          work_hours: data.work_hours,
          phone_hours: data.phone_hours,
          sleep_hours: data.sleep_hours,
        });
        setChecklistItems({
          wakeup: data.wakeup,
          exercise: data.exercise,
          diet: data.diet,
          learn: data.learn,
          work: data.work,
          finances: data.finances,
          steps: data.steps,
          sleep: data.sleep,
        });
        setJournalText(data.journal || "");
        setCurrentDocumentId((data as any).$id);
      } else {
        // Reset to default values if no data exists
        setSelectedMood("Happy");
        setSelectedValues({
          prayers: "",
          steps_count: "0",
          water: "3 LITERS",
          work_hours: "0 HRS",
          phone_hours: "0 HRS",
          sleep_hours: "0 HRS",
        });
        setChecklistItems({
          wakeup: false,
          exercise: false,
          diet: false,
          learn: false,
          work: false,
          finances: false,
          steps: false,
          sleep: false,
        });
        setJournalText("");
        setCurrentDocumentId(null);
      }
    } catch (error) {
      console.error("Error loading tracker data:", error);
      Alert.alert("Error", "Failed to load tracker data");
    } finally {
      setIsLoading(false);
    }
  };

  const saveTrackerData = async () => {
    try {
      if (!user?.$id) {
        console.log("No user ID found, cannot save tracker data");
        return;
      }

      const dateStr = selectedDate.toISOString().split("T")[0];
      console.log("Attempting to save tracker data for date:", dateStr);

      const data: TrackerData = {
        date: dateStr,
        userId: user.$id,
        // Checklist items
        wakeup: checklistItems.wakeup,
        exercise: checklistItems.exercise,
        diet: checklistItems.diet,
        learn: checklistItems.learn,
        work: checklistItems.work,
        finances: checklistItems.finances,
        steps: checklistItems.steps,
        sleep: checklistItems.sleep,
        // Mood
        selectedMood,
        // Values
        prayers: selectedValues.prayers,
        steps_count: selectedValues.steps_count,
        water: selectedValues.water,
        work_hours: selectedValues.work_hours,
        phone_hours: selectedValues.phone_hours,
        sleep_hours: selectedValues.sleep_hours,
        // Journal
        journal: journalText,
        // Income
        income: "+500 ₹",
      };

      console.log("Data to be saved:", JSON.stringify(data, null, 2));

      if (currentDocumentId) {
        console.log("Updating existing document with ID:", currentDocumentId);
        const response = await updateTrackerData(currentDocumentId, data);
        console.log("Update response:", response);
      } else {
        console.log("Creating new document");
        const response = await createTrackerData(data);
        console.log("Create response:", response);
        setCurrentDocumentId((response as any).$id);
      }
    } catch (error) {
      console.error("Error saving tracker data:", error);
      if (error instanceof Error) {
        console.error("Error details:", error.message);
        console.error("Error stack:", error.stack);
      }
      Alert.alert("Error", "Failed to save tracker data");
    }
  };

  const handleDateChange = async (event: any, selectedDate?: Date) => {
    setShowDatePicker(false);
    if (selectedDate) {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      if (selectedDate <= today) {
        setIsLoading(true);
        setSelectedDate(selectedDate);
      }
    }
  };

  useEffect(() => {
    if (isLoading) {
      loadTrackerData();
    }
  }, [selectedDate, isLoading]);

  const toggleSchedule = () => {
    setIsScheduleExpanded(!isScheduleExpanded);
  };

  const handleMoodSelect = async (mood: string) => {
    setSelectedMood(mood);
    await saveTrackerData();
  };

  const handleValueSelect = async (value: string) => {
    setSelectedValues((prev) => ({
      ...prev,
      [valueModalType]: value,
    }));
    await saveTrackerData();
  };

  const openValueModal = (
    type:
      | "prayers"
      | "steps_count"
      | "water"
      | "work_hours"
      | "phone_hours"
      | "sleep_hours",
  ) => {
    setValueModalType(type);
    setShowValueModal(true);
  };

  const getPrayerCount = () => {
    const completedPrayers = selectedValues.prayers.split(" ").filter(Boolean);
    return `${completedPrayers.length}/7 `;
  };

  const toggleChecklistItem = async (item: keyof typeof checklistItems) => {
    setChecklistItems((prev) => ({
      ...prev,
      [item]: !prev[item],
    }));
    await saveTrackerData();
  };

  const handleJournalSave = async (text: string) => {
    setJournalText(text);
    await saveTrackerData();
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <Header />
      {/* Schedule Section */}
      <View style={styles.scheduleSection}>
        <TouchableOpacity
          style={styles.sectionHeader}
          onPress={toggleSchedule}
          activeOpacity={0.7}>
          <View style={styles.sectionHeaderLeft}>
            {isScheduleExpanded ? (
              <ChevronUp color="#666" size={24} />
            ) : (
              <ChevronDown color="#666" size={24} />
            )}
            <View style={{ marginLeft: 20 }}>
              <Text style={styles.sectionTitle}>Schedule</Text>
              <Text style={styles.scheduleTime}>05:30 AM - 08:00 PM</Text>
            </View>
          </View>
          <View style={styles.sectionHeaderRight}>
            <DateTimePicker
              value={selectedDate}
              mode="date"
              display="default"
              onChange={handleDateChange}
              style={{ width: 120 }}
              maximumDate={new Date()}
              themeVariant="light"
            />
          </View>
        </TouchableOpacity>

        {isScheduleExpanded && (
          <>
            {scheduleItems.map((item) => (
              <View key={item.id} style={styles.scheduleItem}>
                <View style={styles.scheduleItemLeft}>
                  <item.icon color="#000" size={26} />
                  <Text style={styles.scheduleItemTitle}>{item.title}</Text>
                </View>
                <View style={styles.scheduleItemRight}>
                  <Text style={styles.scheduleItemTime}>{item.time}</Text>
                </View>
              </View>
            ))}

            <TouchableOpacity style={styles.addNewButton}>
              <Text style={styles.addNewButtonText}>ADD NEW</Text>
            </TouchableOpacity>
          </>
        )}
      </View>

      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Loading data...</Text>
        </View>
      ) : (
        <ScrollView style={styles.scrollView}>
          {/* Checklist */}
          <TouchableOpacity onPress={() => toggleChecklistItem("wakeup")}>
            <TrackingItem
              icon={Sun}
              label="WAKEUP"
              status={checklistItems.wakeup ? "DONE" : undefined}
              statusIcon={checklistItems.wakeup ? CircleCheck : Circle}
            />
          </TouchableOpacity>
          <TouchableOpacity onPress={() => toggleChecklistItem("exercise")}>
            <TrackingItem
              icon={Dumbbell}
              label="EXERCISE"
              status={checklistItems.exercise ? "DONE" : undefined}
              statusIcon={checklistItems.exercise ? CircleCheck : Circle}
            />
          </TouchableOpacity>
          <TouchableOpacity onPress={() => toggleChecklistItem("diet")}>
            <TrackingItem
              icon={Utensils}
              label="DIET"
              status={checklistItems.diet ? "DONE" : undefined}
              statusIcon={checklistItems.diet ? CircleCheck : Circle}
            />
          </TouchableOpacity>
          <TouchableOpacity onPress={() => toggleChecklistItem("learn")}>
            <TrackingItem
              icon={BookOpen}
              label="LEARN"
              status={checklistItems.learn ? "DONE" : undefined}
              statusIcon={checklistItems.learn ? CircleCheck : Circle}
            />
          </TouchableOpacity>
          <TouchableOpacity onPress={() => toggleChecklistItem("work")}>
            <TrackingItem
              icon={Laptop}
              label="WORK"
              status={checklistItems.work ? "DONE" : undefined}
              statusIcon={checklistItems.work ? CircleCheck : Circle}
            />
          </TouchableOpacity>
          <TouchableOpacity onPress={() => toggleChecklistItem("finances")}>
            <TrackingItem
              icon={DollarSign}
              label="FINANCES"
              status={checklistItems.finances ? "DONE" : undefined}
              statusIcon={checklistItems.finances ? CircleCheck : Circle}
            />
          </TouchableOpacity>
          <TouchableOpacity onPress={() => toggleChecklistItem("steps")}>
            <TrackingItem
              icon={Timer}
              label="STEPS"
              status={checklistItems.steps ? "DONE" : undefined}
              statusIcon={checklistItems.steps ? CircleCheck : Circle}
            />
          </TouchableOpacity>
          <TouchableOpacity onPress={() => toggleChecklistItem("sleep")}>
            <TrackingItem
              icon={BedDouble}
              label="SLEEP"
              status={checklistItems.sleep ? "DONE" : undefined}
              statusIcon={checklistItems.sleep ? CircleCheck : Circle}
            />
          </TouchableOpacity>

          <View
            style={{
              borderWidth: 1,
              borderColor: "#F1F1EF",
              margin: 16,
            }}
          />

          {/* Metrics */}

          <TouchableOpacity onPress={() => openValueModal("prayers")}>
            <TrackingItem
              icon={Moon}
              label="PRAYERS"
              value={getPrayerCount()}
            />
          </TouchableOpacity>

          <TouchableOpacity onPress={() => openValueModal("steps_count")}>
            <TrackingItem
              icon={Footprints}
              label="STEPS"
              value={selectedValues.steps_count}
            />
          </TouchableOpacity>

          <TouchableOpacity onPress={() => openValueModal("water")}>
            <TrackingItem
              icon={Droplets}
              label="WATER"
              value={selectedValues.water}
            />
          </TouchableOpacity>

          <TouchableOpacity onPress={() => openValueModal("work_hours")}>
            <TrackingItem
              icon={Clock}
              label="WORKED"
              value={selectedValues.work_hours}
            />
          </TouchableOpacity>

          <TouchableOpacity onPress={() => openValueModal("phone_hours")}>
            <TrackingItem
              icon={Smartphone}
              label="PHONE"
              value={selectedValues.phone_hours}
            />
          </TouchableOpacity>

          <TouchableOpacity onPress={() => openValueModal("sleep_hours")}>
            <TrackingItem
              icon={BedDouble}
              label="SLEEP"
              value={selectedValues.sleep_hours}
            />
          </TouchableOpacity>

          <TrackingItem icon={Wallet} label="INCOME" value="+500 ₹" />

          <View
            style={{
              borderWidth: 1,
              borderColor: "#F1F1EF",
              margin: 16,
            }}
          />

          <TouchableOpacity onPress={() => setShowMoodModal(true)}>
            <TrackingItem icon={Smile} label="MOOD" status={selectedMood} />
          </TouchableOpacity>

          {/* Notes */}
          <View
            style={{
              padding: 16,
              backgroundColor: "#fff",
            }}>
            <TouchableOpacity
              onPress={() => setShowJournalModal(true)}
              style={{
                borderWidth: 1,
                borderColor: "#F1F1EF",
                borderRadius: 8,
                padding: 12,
                minHeight: 100,
              }}>
              <Text style={{ color: "#666" }}>
                {journalText || "Write about your day and feelings..."}
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      )}

      <MoodSelectionModal
        visible={showMoodModal}
        onClose={() => setShowMoodModal(false)}
        onSelectMood={handleMoodSelect}
      />

      <ValueSelectionModal
        visible={showValueModal}
        onClose={() => setShowValueModal(false)}
        onSelectValue={handleValueSelect}
        type={valueModalType}
        currentValue={selectedValues[valueModalType]}
      />

      <JournalModal
        visible={showJournalModal}
        onClose={() => setShowJournalModal(false)}
        onSave={handleJournalSave}
        initialText={journalText}
      />
    </View>
  );
};

export default HomeLayout;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    backgroundColor: "#fff",
    paddingVertical: 16,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    justifyContent: "space-between",
  },
  searchContainer: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#1a1a1a",
    borderRadius: 8,
    padding: 8,
    marginRight: 16,
  },
  searchPlaceholder: {
    color: "#666",
    marginLeft: 8,
  },
  notificationButton: {
    padding: 8,
  },
  scheduleSection: {
    paddingHorizontal: 16,
    backgroundColor: "#fff",
    marginBottom: 2,
  },
  sectionHeader: {
    marginVertical: 16,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  sectionHeaderLeft: {
    marginLeft: 12,
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "bold",
  },
  scheduleTime: {
    marginTop: 4,
  },
  scheduleItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#F1F1EF",
  },
  scheduleItemLeft: {
    flexDirection: "row",
    alignItems: "center",
  },
  scheduleItemTitle: {
    marginLeft: 12,
    fontSize: 16,
    fontWeight: "bold",
  },
  scheduleItemRight: {
    backgroundColor: "#D35400",
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 6,
  },
  scheduleItemTime: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "bold",
  },
  addNewButton: {
    borderWidth: 1,
    borderColor: "#333",
    borderStyle: "dashed",
    borderRadius: 8,
    padding: 16,
    alignItems: "center",
    marginTop: 16,
    marginBottom: 16,
  },
  addNewButtonText: {
    color: "#666",
  },
  sectionHeaderRight: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#fff",
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: "#666",
  },
});

import { Stack, Tabs } from "expo-router";
import { Book, Clock, Home, Store, Wallet } from "lucide-react-native";

const Layout = () => {
  return (
    <Tabs
      screenOptions={{
        tabBarShowLabel: false,
        tabBarActiveTintColor: "#000",
      }}>
      <Tabs.Screen
        name="index"
        options={{
          headerShown: false,
          tabBarIcon: ({ color, size, focused }) => (
            <Home color={color} size={size} />
          ),
        }}
      />
      <Tabs.Screen
        name="notes"
        options={{
          headerShown: false,
          tabBarIcon: ({ color, size, focused }) => (
            <Book color={color} size={size} />
          ),
        }}
      />
      <Tabs.Screen
        name="tracker"
        options={{
          headerShown: false,

          tabBarIcon: ({ color, size, focused }) => (
            <Clock color={color} size={size} />
          ),
        }}
      />
      <Tabs.Screen
        name="wallet"
        options={{
          headerShown: false,
          tabBarIcon: ({ color, size, focused }) => (
            <Wallet color={color} size={size} />
          ),
        }}
      />
      <Tabs.Screen
        name="business"
        options={{
          headerShown: false,
          tabBarIcon: ({ color, size, focused }) => (
            <Store color={color} size={size} />
          ),
        }}
      />
    </Tabs>
  );
};

export default Layout;

import { StyleSheet, Text, View, <PERSON><PERSON><PERSON>iew } from "react-native";
import React, { useState } from "react";
import Header from "@/components/Header";
import FamilyMember from "@/components/FamilyMember";
import TabBar from "@/components/TabBar";

const TABS = ["FAMILY", "LEARN", "WORK"];

const familyMembers = [
  { name: "SHAWAZ", relation: "ME" },
  { name: "SHARIF", relation: "DAD" },
  { name: "SELINA", relation: "MOM" },
  { name: "<PERSON>HAIRUNNI<PERSON>", relation: "GROOM" },
  { name: "SHAMFRA<PERSON>", relation: "BROTHER" },
  { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", relation: "SISTER" },
  { name: "<PERSON><PERSON><PERSON><PERSON>", relation: "NEPHEW" },
  { name: "<PERSON>Y<PERSON>", relation: "NEPHEW" },
  { name: "<PERSON>AY<PERSON>", relation: "NEPHEW" },
  { name: "<PERSON><PERSON><PERSON>", relation: "NEPHE<PERSON>" },
  { name: "<PERSON><PERSON><PERSON>", relation: "COUSIN" },
  { name: "<PERSON><PERSON><PERSON>", relation: "COUSIN" },
  { name: "<PERSON><PERSON><PERSON>", relation: "COUS<PERSON>" },
  { name: "<PERSON><PERSON><PERSON>", relation: "COUSIN" },
  { name: "ESHAH", relation: "COUSIN" },
  { name: "ESHAL", relation: "COUSIN" },
  { name: "AAYATH", relation: "COUSIN" },
  { name: "ZARA", relation: "COUSIN" },
  { name: "BARKATH", relation: "UNCLE" },
  { name: "NAZAR", relation: "UNCLE" },
  { name: "IBRAHIM", relation: "AUNTY" },
  { name: "SHABEENA", relation: "AUNTY" },
  { name: "MUZU", relation: "AUNTY" },
  { name: "SAFIA", relation: "AUNTY" },
];

const NotesScreen = () => {
  const [activeTab, setActiveTab] = useState("FAMILY");

  const renderContent = () => {
    switch (activeTab) {
      case "FAMILY":
        return (
          <View style={styles.grid}>
            {familyMembers.map((member, index) => (
              <View key={index} style={styles.gridItem}>
                <FamilyMember name={member.name} relation={member.relation} />
              </View>
            ))}
          </View>
        );
      case "LEARN":
        return (
          <View style={styles.emptyState}>
            <Text>Learn content coming soon</Text>
          </View>
        );
      case "WORK":
        return (
          <View style={styles.emptyState}>
            <Text>Work content coming soon</Text>
          </View>
        );
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      <Header />
      <TabBar activeTab={activeTab} onTabPress={setActiveTab} />
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {renderContent()}
      </ScrollView>
    </View>
  );
};

export default NotesScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  scrollContent: {
    padding: 16,
  },
  grid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    gap: 16,
  },
  gridItem: {
    width: "47%",
  },
  emptyState: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 32,
  },
});

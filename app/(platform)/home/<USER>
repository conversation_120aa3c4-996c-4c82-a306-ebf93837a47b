import { Image, ScrollView, StyleSheet, Text, View } from "react-native";
import React from "react";
import Header from "@/components/Header";
import {
  ArrowUpDownIcon,
  CircleCheck,
  ClipboardList,
  Clock,
  Newspaper,
  Plane,
  FolderOpen,
  Files,
  Coins,
  Bitcoin,
  TrendingUp,
  Rocket,
  FileText,
  Lightbulb,
  Activity,
  Users,
  Radio,
  Folder,
  Wallet,
  Receipt,
  FileSpreadsheet,
  DollarSign,
  CreditCard,
  Building2,
  CalendarDays,
  Briefcase,
  UserPlus,
  Users2,
  LogOut,
  ShoppingBag,
  Megaphone,
  FileVideo,
  Handshake,
  UserCircle2,
  Target,
  MonitorSmartphone,
  Puzzle,
  Bug,
  Ticket,
  HelpCircle,
  LucideIcon,
} from "lucide-react-native";
import CategoryCard, { categoryColors } from "@/components/CategoryCard";

type IconMapType = {
  [key: string]: {
    [key: string]: LucideIcon;
  };
};

const getItemIcon = (category: string, item: string): LucideIcon => {
  const iconMap: IconMapType = {
    HOME: {
      Tasks: ClipboardList,
      Attendance: Clock,
      News: Newspaper,
      Trips: Plane,
      Resources: FolderOpen,
      Files: Files,
    },
    OPERATIONS: {
      Coins: Coins,
      Crypto: Bitcoin,
      Stocks: TrendingUp,
      Startups: Rocket,
    },
    ADMINISTRATION: {
      Plan: FileText,
      Strategy: Lightbulb,
      Activity: Activity,
      Partners: Users,
      Channels: Radio,
      Resources: Folder,
    },
    FINANCE: {
      Budget: Wallet,
      Transactions: Receipt,
      Invoices: FileSpreadsheet,
      Expenses: DollarSign,
      Salaries: CreditCard,
      Payees: Building2,
      Bank: Building2,
    },
    PEOPLE: {
      Leave: CalendarDays,
      Job: Briefcase,
      Openings: UserPlus,
      Employees: Users2,
      Onboarding: UserPlus,
      Offboarding: LogOut,
    },
    MSSS: {
      Market: ShoppingBag,
      Campaigns: Megaphone,
      Content: FileVideo,
      Deals: Handshake,
      Clients: UserCircle2,
      Competitors: Target,
      Platform: MonitorSmartphone,
      Features: Puzzle,
      Bugs: Bug,
      Tickets: Ticket,
      Support: HelpCircle,
    },
  };

  const DefaultIcon = CircleCheck;
  return iconMap[category]?.[item] || DefaultIcon;
};

const getItemStatus = (category: string, item: string): { text: string } => {
  const defaultStatus = { text: "Active" };

  const statusMap: { [key: string]: { [key: string]: { text: string } } } = {
    HOME: {
      Tasks: { text: "Pending" },
      Attendance: { text: "Present" },
    },
    OPERATIONS: {
      Stocks: { text: "Trading" },
      Crypto: { text: "Volatile" },
    },
    FINANCE: {
      Budget: { text: "Review" },
      Transactions: { text: "Pending" },
    },
    PEOPLE: {
      Leave: { text: "Open" },
      Job: { text: "Hiring" },
    },
    MSSS: {
      Bugs: { text: "Critical" },
      Tickets: { text: "Open" },
    },
  };

  return statusMap[category]?.[item] || defaultStatus;
};

const BusinessScreen = () => {
  const categories: Record<keyof typeof categoryColors, string[]> = {
    HOME: ["Tasks", "Attendance", "News", "Trips", "Resources", "Files"],
    OPERATIONS: ["Coins", "Crypto", "Stocks", "Startups"],
    ADMINISTRATION: [
      "Plan",
      "Strategy",
      "Activity",
      "Partners",
      "Channels",
      "Resources",
    ],
    FINANCE: [
      "Budget",
      "Transactions",
      "Invoices",
      "Expenses",
      "Salaries",
      "Payees",
      "Bank",
    ],
    PEOPLE: [
      "Leave",
      "Job",
      "Openings",
      "Employees",
      "Onboarding",
      "Offboarding",
    ],
    MSSS: [
      "Market",
      "Campaigns",
      "Content",
      "Deals",
      "Clients",
      "Competitors",
      "Platform",
      "Features",
      "Bugs",
      "Tickets",
      "Support",
    ],
  };

  return (
    <View style={styles.container}>
      <Header />
      <View style={styles.profileCard}>
        <View style={styles.profileLeft}>
          <Image
            source={require("@/assets/icons/dark.png")}
            style={styles.logo}
          />
          <View style={styles.info}>
            <Text style={styles.name}>Sharif</Text>
            <Text style={styles.address}>Administration</Text>
            <Text style={styles.address}>CFO</Text>
          </View>
        </View>
        <ArrowUpDownIcon size={24} color="#000" style={{ marginRight: 8 }} />
      </View>

      <ScrollView style={styles.scrollView}>
        {Object.entries(categories).map(([category, items]) => (
          <CategoryCard
            key={category}
            title={category as keyof typeof categoryColors}
            items={items}
          />
        ))}
      </ScrollView>
    </View>
  );
};

export default BusinessScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  scrollView: {
    flex: 1,
  },
  profileCard: {
    padding: 16,
    backgroundColor: "#fff",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 2,
  },
  profileLeft: {
    flexDirection: "row",
    alignItems: "center",
    gap: 16,
    flex: 1,
  },
  logo: {
    width: 75,
    height: 75,
    borderRadius: 8,
  },
  info: {
    flex: 1,
  },
  name: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 8,
  },
  address: {
    fontSize: 16,
    marginBottom: 4,
    color: "#666",
  },
});

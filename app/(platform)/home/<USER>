import { ScrollView, StyleSheet, View } from "react-native";
import React, { useState, useEffect } from "react";
import Header from "@/components/Header";
import CategorySection from "@/components/CategorySection";
import Onboarding from "@/components/Onboarding";
import { useAuth } from "@/context/auth";
import { mcpService } from "@/lib/appwrite/mcp-integration";

const VisionLayout = () => {
  const { user } = useAuth();
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [categories, setCategories] = useState([
    {
      title: "MIND & BODY",
      timeRange: "6AM - 8AM",
      backgroundColor: "#E3F2FD",
      tasks: [
        { title: "PRAYERS", status: "on-track" as const },
        { title: "EXERCISE", status: "on-track" as const },
        { title: "DIET", status: "on-track" as const },
        { title: "FAMILY", status: "not-started" as const },
        { title: "SOCIAL", status: "completed" as const },
      ],
    },
    {
      title: "LEARN",
      timeRange: "6AM - 8AM",
      backgroundColor: "#FFF3E0",
      tasks: [
        { title: "SKILLS", status: "on-track" as const },
        { title: "CERTIFICATE", status: "on-track" as const },
        { title: "INTERNSHIP", status: "on-track" as const },
        { title: "SCHOOL", status: "completed" as const },
        { title: "COLLEGE", status: "on-track" as const },
      ],
    },
    {
      title: "WORK",
      timeRange: "6AM - 8AM",
      backgroundColor: "#E8F5E9",
      tasks: [
        { title: "JOB", status: "on-track" as const },
        { title: "FRANCHISE", status: "on-track" as const },
        { title: "CRYPTO", status: "on-track" as const },
        { title: "STOCKS", status: "on-track" as const },
        { title: "PROPERTIES", status: "on-track" as const },
      ],
    },
    {
      title: "FINANCE",
      timeRange: "6AM - 8AM",
      backgroundColor: "#F3E5F5",
      tasks: [
        { title: "NET WORTH", status: "on-track" as const },
        { title: "INVESTMENTS", status: "on-track" as const },
        { title: "CREDITS", status: "on-track" as const },
        { title: "CHARITY", status: "on-track" as const },
        { title: "ASSETS", status: "on-track" as const },
      ],
    },
    {
      title: "WEEKEND",
      timeRange: "SAT - SUN",
      backgroundColor: "#FFEBEE",
      tasks: [
        { title: "EVENTS", status: "on-track" as const },
        { title: "MEMORIES", status: "on-track" as const },
        { title: "SHOPPING", status: "on-track" as const },
        { title: "TRAVELS", status: "on-track" as const },
        { title: "DOCUMENTS", status: "on-track" as const },
      ],
    },
  ]);

  useEffect(() => {
    checkOnboardingStatus();
  }, [user]);

  const checkOnboardingStatus = async () => {
    if (!user) return;

    try {
      // Temporarily disabled for debugging
      // const profile = await mcpService.getUserProfile(user.$id);
      // Show onboarding if profile is not complete
      // if (!profile?.profileComplete) {
      //   setShowOnboarding(true);
      // }
    } catch (error) {
      // If profile doesn't exist, show onboarding
      // setShowOnboarding(true);
    }
  };

  const handleStatusChange = (
    categoryIndex: number,
    taskIndex: number,
    newStatus: string,
  ) => {
    setCategories((prevCategories) => {
      const newCategories = [...prevCategories];
      newCategories[categoryIndex].tasks[taskIndex].status = newStatus as any;
      return newCategories;
    });
  };

  return (
    <View style={styles.container}>
      <Header />
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollViewContent}
        style={styles.scrollView}>
        {categories.map((category, categoryIndex) => (
          <View key={categoryIndex} style={styles.categoryWrapper}>
            <CategorySection
              title={category.title}
              timeRange={category.timeRange}
              tasks={category.tasks}
              backgroundColor={category.backgroundColor}
              onStatusChange={(taskIndex, newStatus) =>
                handleStatusChange(categoryIndex, taskIndex, newStatus)
              }
            />
          </View>
        ))}
      </ScrollView>

      <Onboarding
        visible={showOnboarding}
        onClose={() => setShowOnboarding(false)}
      />
    </View>
  );
};

export default VisionLayout;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    padding: 8,
  },
  categoryWrapper: {
    width: 300, // Fixed width for each category
    maxHeight: "100%",
  },
});

// import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
// import { useAuth } from "../../../context/auth";
// import Header from "@/components/Header";

// export default function Home() {
//   const { user, signOut } = useAuth();

//   return (
//     <View style={styles.container}>
//       <Header />
//       <Text style={styles.title}>Welcome, {user?.name || "User"}!</Text>
//       <Text style={styles.title}>Welcome, {user?.email || "Email"}!</Text>
//       <TouchableOpacity style={styles.button} onPress={signOut}>
//         <Text style={styles.buttonText}>Sign Out</Text>
//       </TouchableOpacity>
//     </View>
//   );
// }

// const styles = StyleSheet.create({
//   container: {},
//   title: {
//     fontSize: 24,
//     fontWeight: "bold",
//     marginBottom: 20,
//   },
//   button: {
//     backgroundColor: "#FF3B30",
//     padding: 15,
//     borderRadius: 5,
//     width: "100%",
//     alignItems: "center",
//   },
//   buttonText: {
//     color: "#fff",
//     fontSize: 16,
//     fontWeight: "bold",
//   },
// });

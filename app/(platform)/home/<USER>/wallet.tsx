import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  Image,
} from "react-native";
import React, { useState } from "react";
import { Plus } from "lucide-react-native";
import Header from "@/components/Header";
import WalletActionModal from "@/components/WalletActionModal";
import AddWalletModal from "@/components/AddWalletModal";
import AddBudgetModal from "@/components/AddBudgetModal";

interface SummaryItemProps {
  label: string;
  amount: string;
  type: "income" | "expenses" | "savings";
}

interface WalletCardProps {
  title: string;
  amount: string;
  type: "savings" | "credit" | "other";
  imageSource: any;
}

interface BudgetItemProps {
  title: string;
  amount: string;
  category: "investment" | "business" | "expense";
  isNegative?: boolean;
  status: string;
  balance: string;
  imageUri?: string;
}

interface SubscriptionItemProps {
  title: string;
  amount: string;
  dueDate: string;
}

interface TransactionProps {
  title: string;
  date: string;
  amount: string;
  account: string;
  type: "credit" | "debit";
}

interface Budget {
  id: string;
  name: string;
  goal: string;
  walletId: string;
  imageUri?: string;
  walletName: string;
  walletBalance: string;
}

const SummaryItem: React.FC<SummaryItemProps> = ({ label, amount, type }) => {
  const color =
    type === "income" ? "#4CAF50" : type === "expenses" ? "#F44336" : "#9C27B0";

  return (
    <View style={styles.summaryItem}>
      <Text style={[styles.label, { color }]}>{label}</Text>
      <Text style={[styles.amount, { color }]}>{amount}</Text>
    </View>
  );
};

const WalletCard: React.FC<WalletCardProps> = ({
  title,
  amount,
  type,
  imageSource,
}) => {
  const color =
    type === "savings" ? "#4CAF50" : type === "credit" ? "#F44336" : "#9C27B0";

  return (
    <View style={styles.walletCard}>
      <Image source={imageSource} style={styles.walletIcon} />
      <View style={styles.walletInfo}>
        <Text style={styles.walletTitle}>{title}</Text>
        <Text style={[styles.walletAmount, { color }]}>{amount}</Text>
      </View>
    </View>
  );
};

const BudgetItem: React.FC<BudgetItemProps> = ({
  title,
  amount,
  balance,
  category,
  isNegative,
  imageUri,
}) => {
  const color =
    category === "investment"
      ? "#9C27B0"
      : category === "business"
      ? "#4CAF50"
      : "#F44336";

  return (
    <View style={styles.card}>
      {imageUri ? (
        <Image source={{ uri: imageUri }} style={styles.budgetIcon} />
      ) : (
        <View style={styles.budgetIcon} />
      )}
      <View style={styles.budgetInfo}>
        <Text style={styles.budgetTitle}>{title}</Text>
        <Text style={[styles.budgetAmount, { color }]}>
          {isNegative ? "- " : ""}${amount}
        </Text>
      </View>
      <View style={{ alignItems: "flex-end" }}>
        <Text style={styles.budgetStatus}>{category}</Text>
        <Text style={[styles.budgetAmount, { color }]}>
          {isNegative ? "- " : ""}${balance}
        </Text>
      </View>
    </View>
  );
};

const SubscriptionItem: React.FC<SubscriptionItemProps> = ({
  title,
  amount,
  dueDate,
}) => {
  return (
    <View style={styles.card}>
      <View style={styles.subscriptionIcon} />
      <View style={styles.subscriptionInfo}>
        <Text style={styles.subscriptionTitle}>{title}</Text>
        <Text style={styles.subscriptionDueDate}>Due: {dueDate}</Text>
      </View>
      <Text style={[styles.subscriptionAmount, { color: "#F44336" }]}>
        ${amount}
      </Text>
    </View>
  );
};

const Transaction: React.FC<TransactionProps> = ({
  title,
  date,
  amount,
  account,
  type,
}) => {
  return (
    <View style={styles.card}>
      <View style={styles.transactionIcon} />
      <View style={styles.transactionInfo}>
        <Text style={styles.transactionTitle}>{title}</Text>
        <Text style={styles.transactionDate}>{date}</Text>
      </View>
      <View style={styles.transactionAmount}>
        <Text
          style={[
            styles.amount,
            { color: type === "credit" ? "#4CAF50" : "#F44336" },
          ]}>
          {type === "credit" ? "+" : "-"} ${amount}
        </Text>
        <Text style={styles.accountName}>{account}</Text>
      </View>
    </View>
  );
};

const WalletLayout = () => {
  const [showActionModal, setShowActionModal] = useState(false);
  const [showAddWalletModal, setShowAddWalletModal] = useState(false);
  const [showAddBudgetModal, setShowAddBudgetModal] = useState(false);
  const [budgets, setBudgets] = useState<Budget[]>([]);

  const wallets = [
    { id: "1", name: "BullX W7", balance: "$8,000" },
    { id: "2", name: "Phantom", balance: "$8,000" },
    { id: "3", name: "Binance", balance: "$5,000" },
    { id: "4", name: "Juspay", balance: "$2,500" },
    { id: "5", name: "Jupiter Savings", balance: "$10,000" },
    { id: "6", name: "Jupiter Credit", balance: "$5,000" },
    { id: "7", name: "ENBD Savings", balance: "$20,000" },
    { id: "8", name: "ENBD Credit", balance: "$12,000" },
  ];

  const handleActionSelect = (action: string) => {
    if (action === "Add Wallet") {
      setShowAddWalletModal(true);
    } else if (action === "Add Budget") {
      setShowAddBudgetModal(true);
    }
  };

  const handleSaveWallet = (wallet: {
    name: string;
    balance: string;
    imageUri: string;
  }) => {
    console.log("Saving wallet:", wallet);
  };

  const handleSaveBudget = (budget: {
    name: string;
    goal: string;
    walletId: string;
    imageUri?: string;
  }) => {
    const selectedWallet = wallets.find((w) => w.id === budget.walletId);
    if (selectedWallet) {
      const newBudget: Budget = {
        id: Date.now().toString(),
        ...budget,
        walletName: selectedWallet.name,
        walletBalance: selectedWallet.balance,
      };
      setBudgets([...budgets, newBudget]);
    }
  };

  return (
    <View style={styles.container}>
      <Header />
      {/* Total Overview */}
      <View style={styles.card}>
        <Text style={styles.sectionTitle}>TOTAL</Text>
        <SummaryItem label="INCOME" amount="$75,000" type="income" />
        <SummaryItem label="EXPENSES" amount="$45,000" type="expenses" />
        <SummaryItem label="SAVINGS" amount="$30,000" type="savings" />
      </View>

      {/* Current Year Total */}
      <View style={styles.card}>
        <Text style={styles.sectionTitle}>2025</Text>
        <SummaryItem label="INCOME" amount="$5,000" type="income" />
        <SummaryItem label="EXPENSES" amount="$4,000" type="expenses" />
        <SummaryItem label="SAVINGS" amount="$1,000" type="savings" />
      </View>
      <ScrollView>
        {/* Wallet */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>WALLET</Text>
            <TouchableOpacity onPress={() => setShowAddWalletModal(true)}>
              <Text style={styles.addNew}>ADD NEW</Text>
            </TouchableOpacity>
          </View>
        </View>
        <View style={styles.walletGrid}>
          <WalletCard
            title="BullX W7"
            amount="$8,000"
            type="other"
            imageSource={require("@/assets/images/bullx.png")}
          />
          <WalletCard
            title="Phantom"
            amount="$8,000"
            type="other"
            imageSource={require("@/assets/images/phantom.png")}
          />
          <WalletCard
            title="Binance"
            amount="$5,000"
            type="other"
            imageSource={require("@/assets/images/binance.png")}
          />
          <WalletCard
            title="Juspay"
            amount="$2,500"
            type="other"
            imageSource={require("@/assets/images/juspay.png")}
          />
          <WalletCard
            title="Jupiter Savings"
            amount="$10,000"
            type="savings"
            imageSource={require("@/assets/images/jupiter.png")}
          />
          <WalletCard
            title="Jupiter Credit"
            amount="$5,000"
            type="credit"
            imageSource={require("@/assets/images/jupiter.png")}
          />
          <WalletCard
            title="ENBD Savings"
            amount="$20,000"
            type="savings"
            imageSource={require("@/assets/images/enbd.png")}
          />
          <WalletCard
            title="ENBD Credit"
            amount="$12,000"
            type="credit"
            imageSource={require("@/assets/images/enbd.png")}
          />
        </View>

        {/* Budget */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>BUDGET</Text>
            <TouchableOpacity onPress={() => setShowAddBudgetModal(true)}>
              <Text style={styles.addNew}>ADD NEW</Text>
            </TouchableOpacity>
          </View>
        </View>
        {budgets.map((budget) => (
          <BudgetItem
            key={budget.id}
            title={budget.name}
            amount={budget.goal}
            category="investment"
            status="100%"
            balance={budget.walletBalance}
            imageUri={budget.imageUri}
          />
        ))}
        <BudgetItem
          title="Gold"
          amount="50,000"
          category="investment"
          status="100%"
          balance="50,000"
        />
        <BudgetItem
          title="SIP"
          amount="25,000"
          category="investment"
          status="100%"
          balance="25,000"
        />
        <BudgetItem
          title="Bullx"
          amount="15,000"
          category="business"
          status="100%"
          balance="15,000"
        />
        <BudgetItem
          title="Franchiseen"
          amount="30,000"
          category="business"
          status="100%"
          balance="30,000"
        />
        <BudgetItem
          title="Farmdrone"
          amount="20,000"
          category="business"
          status="100%"
          balance="20,000"
        />
        <BudgetItem
          title="Personal"
          amount="5,000"
          category="expense"
          status="100%"
          balance="5,000"
        />
        <BudgetItem
          title="Dad"
          amount="2,000"
          category="expense"
          status="100%"
          balance="2,000"
        />
        <BudgetItem
          title="Mom"
          amount="2,000"
          category="expense"
          status="100%"
          balance="2,000"
        />
        <BudgetItem
          title="GMom"
          amount="1,000"
          category="expense"
          status="100%"
          balance="1,000"
        />
        <TouchableOpacity>
          <View
            style={{
              justifyContent: "center",
              alignItems: "center",
              backgroundColor: "#fff",
              padding: 16,
            }}>
            <Text style={styles.viewMore}>VIEW MORE</Text>
          </View>
        </TouchableOpacity>

        {/* Subscriptions */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>SUBSCRIPTIONS</Text>
            <TouchableOpacity>
              <Text style={styles.addNew}>ADD NEW</Text>
            </TouchableOpacity>
          </View>
        </View>
        <SubscriptionItem title="Jio" amount="499" dueDate="15th" />
        <SubscriptionItem title="Youtube" amount="199" dueDate="1st" />
        <SubscriptionItem title="Google" amount="299" dueDate="5th" />
        <SubscriptionItem title="House Rent" amount="15,000" dueDate="1st" />
        <SubscriptionItem title="Cursor AI" amount="20" dueDate="1st" />
        <SubscriptionItem title="Midjourney" amount="30" dueDate="1st" />

        {/* Transactions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>TRANSACTIONS</Text>
        </View>
        <Transaction
          title="Fuel"
          date="07 JUN 25"
          amount="45"
          account="ICICI Credit"
          type="debit"
        />
        <Transaction
          title="Franchiseen"
          date="06 JUN 25"
          amount="5,000"
          account="ICICI Business Savings"
          type="credit"
        />
        <Transaction
          title="House Rent"
          date="05 JUN 25"
          amount="15,000"
          account="Emirates NBD Credit"
          type="debit"
        />
        <Transaction
          title="Bullx Trading"
          date="04 JUN 25"
          amount="2,500"
          account="Binance"
          type="credit"
        />
        <Transaction
          title="Farmdrone Revenue"
          date="03 JUN 25"
          amount="8,000"
          account="Jupiter Savings"
          type="credit"
        />
      </ScrollView>

      {/* Floating Action Button */}
      <TouchableOpacity
        style={styles.fab}
        onPress={() => setShowActionModal(true)}>
        <Plus size={24} color="#fff" />
      </TouchableOpacity>

      <WalletActionModal
        visible={showActionModal}
        onClose={() => setShowActionModal(false)}
        onSelectAction={handleActionSelect}
      />

      <AddWalletModal
        visible={showAddWalletModal}
        onClose={() => setShowAddWalletModal(false)}
        onSave={handleSaveWallet}
      />

      <AddBudgetModal
        visible={showAddBudgetModal}
        onClose={() => setShowAddBudgetModal(false)}
        onSave={handleSaveBudget}
        wallets={wallets}
      />
    </View>
  );
};

export default WalletLayout;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  card: {
    padding: 16,
    backgroundColor: "#fff",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    justifyContent: "space-between",
  },
  viewMore: {
    color: "#D9730D",
    fontSize: 16,
    fontWeight: "bold",
  },
  searchBar: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f5f5f5",
    padding: 8,
    borderRadius: 8,
    flex: 1,
    marginRight: 16,
  },
  searchText: {
    marginLeft: 8,
    color: "#666",
  },
  summary: {
    padding: 16,
  },
  summaryRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 8,
  },
  summaryItem: {
    alignItems: "center",
  },
  section: {
    padding: 16,
    backgroundColor: "#fff",
    marginTop: 8,
    marginBottom: 1,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "bold",
  },
  addNew: {
    color: "#D9730D",
    fontSize: 16,
    fontWeight: "bold",
  },
  walletGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  walletCard: {
    width: "50%",
    borderColor: "#f5f5f5",
    backgroundColor: "#fff",
    borderWidth: 1,
    padding: 16,
    flexDirection: "row",
    alignItems: "center",
  },
  walletIcon: {
    width: 40,
    height: 40,
    borderRadius: 5,
    marginRight: 12,
  },
  walletInfo: {
    flex: 1,
  },
  walletTitle: {
    fontSize: 14,
    color: "#666",
    marginBottom: 4,
  },
  walletAmount: {
    fontSize: 16,
    fontWeight: "bold",
  },
  budgetItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  budgetIcon: {
    width: 40,
    height: 40,
    backgroundColor: "#e0e0e0",
    borderRadius: 20,
    marginRight: 12,
    overflow: "hidden",
  },
  budgetInfo: {
    flex: 1,
    marginRight: 12,
  },
  budgetTitle: {
    fontSize: 16,
    marginBottom: 4,
    fontWeight: "bold",
  },
  budgetStatus: {
    fontSize: 12,
    marginBottom: 4,
    textTransform: "uppercase",
    fontWeight: "500",
    color: "#666",
  },
  budgetAmount: {
    fontSize: 14,
    fontWeight: "bold",
  },
  subscriptionItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  subscriptionIcon: {
    width: 40,
    height: 40,
    backgroundColor: "#e0e0e0",
    borderRadius: 20,
    marginRight: 12,
  },
  subscriptionInfo: {
    flex: 1,
  },
  subscriptionTitle: {
    fontSize: 14,
    marginBottom: 4,
  },
  subscriptionDueDate: {
    fontSize: 12,
    color: "#666",
  },
  subscriptionAmount: {
    fontSize: 16,
    fontWeight: "bold",
  },
  transaction: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  transactionIcon: {
    width: 40,
    height: 40,
    backgroundColor: "#e0e0e0",
    borderRadius: 20,
    marginRight: 12,
  },
  transactionInfo: {
    flex: 1,
  },
  transactionTitle: {
    fontSize: 14,
    marginBottom: 4,
  },
  transactionDate: {
    fontSize: 12,
    color: "#666",
  },
  transactionAmount: {
    alignItems: "flex-end",
  },
  amount: {
    fontSize: 16,
    fontWeight: "bold",
  },
  accountName: {
    fontSize: 12,
    color: "#666",
  },
  label: {
    fontSize: 12,
    marginBottom: 4,
  },
  fab: {
    position: "absolute",
    right: 16,
    bottom: 16,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: "#D9730D",
    justifyContent: "center",
    alignItems: "center",
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
});
